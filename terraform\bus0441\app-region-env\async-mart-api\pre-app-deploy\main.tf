provider "aws" {
  region = var.region
  assume_role {
    role_arn     = var.use_role_to_assume ? var.role_to_assume : null
    session_name = var.session_name
  }
}

provider "aws" {
  region = var.us-east-1
  alias  = "main"

  assume_role {
    role_arn     = var.use_role_to_assume ? var.role_to_assume : null
    session_name = "${var.session_name}_us-east"
  }
  default_tags {
    tags = local.tags
  }
}

provider "aws" {
  region = var.eu-west-1
  alias  = "alt"

  assume_role {
    role_arn     = var.use_role_to_assume ? var.role_to_assume : null
    session_name = "${var.session_name}_eu_west"
  }
  default_tags {
    tags = local.tags
  }
}
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.58"
    }
  }
  backend "s3" {}
}