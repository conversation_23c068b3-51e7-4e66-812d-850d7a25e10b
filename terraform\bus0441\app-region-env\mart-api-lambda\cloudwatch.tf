resource "aws_cloudwatch_log_subscription_filter" "rdb_lambda_log_subscription_filter" {
  count           = "1"
  name            = "rdb_lambda_log_subscription"
  log_group_name  = aws_cloudwatch_log_group.clean_mart_rdb_cache_log_group.name
  filter_pattern  = ""
  destination_arn = "${data.aws_kinesis_stream.stream.arn}"
  role_arn        = "${data.aws_iam_role.log_role.arn}"
}

resource "aws_cloudwatch_log_group" "clean_mart_rdb_cache_log_group" {
  name              = "/aws/lambda/clean-mart-rdb-cache-${var.environment}"
  retention_in_days = 30
  skip_destroy      = false
  tags              = local.tags
}


resource "aws_cloudwatch_metric_alarm" "rdb-sqs-alarm" {
  alarm_name                = "rdb-sqs-alarm-${var.environment}"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  metric_name               = "ApproximateAgeOfOldestMessage"
  namespace                 = "AWS/SQS"
  evaluation_periods        = var.rdb_sqs_alarm_evaluation_periods
  period                    = var.rdb_sqs_alarm_period
  statistic                 = "Average"
  threshold                 = var.rdb_sqs_alarm_threashold
  alarm_description         = "This metric monitors the rdb-sqs queue"
  dimensions = {
    QueueName = aws_sqs_queue.rdb_sqs.name
  }
  alarm_actions = [aws_sns_topic.rdb_sqs_alarm_email_topic.arn]
  tags = local.tags
}