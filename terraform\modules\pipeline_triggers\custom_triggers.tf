resource "harness_platform_triggers" "this" {
  count       = var.custom_triggers == null ? 0 : length(var.custom_triggers)

  identifier  = var.custom_triggers[count.index].identifier
  name        = var.custom_triggers[count.index].name
  tags        = ["terraform_managed"]
  org_id      = var.org_id
  project_id  = var.project_id
  target_id   = var.pipeline_id
  yaml        = templatefile(var.custom_triggers[count.index].template.file_path,
                  merge(var.custom_triggers[count.index].template.variables, {
                    org_id        = var.org_id
                    project_id    = var.project_id
                    pipeline_id   = var.pipeline_id
                    repo_name     = var.repo_name
                    trigger_id    = var.custom_triggers[count.index].identifier
                    trigger_name  = var.custom_triggers[count.index].name
                  })
                )
  depends_on = [harness_platform_input_set.this]
}
