data "aws_iam_role" "log_role" {
  name  = "CWLtoKinesis"
}

data "aws_kinesis_stream" "stream" {
  name = "mstar-kinesis-dpda-${var.environment == "prod" ? "prod" : "non-prod"}-splunk-${var.region}"
}

module "region_data" {
  source = "git::ssh://msstash.morningstar.com/cloudsvc/tf-module-infrastructure-data.git?ref=feature/terraform1.3"
}

data "aws_iam_role" "mart_role" {
  name = "mart-role-${var.environment}"
}

data "aws_sqs_queue" "ph-async-mart-api-message" {
  name = "ph-async-mart-api-message-${var.environment}"
}

data "aws_sns_topic" "asyncapi_notif" {
  count  = var.environment == "stg" || var.environment == "prod" ? 1 : 0
  name   = "async-mart-api-overflow-notif-${var.environment}"
}
