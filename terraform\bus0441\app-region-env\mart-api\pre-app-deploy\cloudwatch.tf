resource "aws_cloudwatch_log_group" "mart_api_fargate_log" {
  name              = "/ecs/mart-api-fargate-${var.environment}"
  retention_in_days = "7"
  tags              = local.tags
}

resource "aws_cloudwatch_log_subscription_filter" "mart_api_fargate_log_subscription" {
  name            = "mart-api-fargate-log-subscription-${var.environment}"
  log_group_name  = aws_cloudwatch_log_group.mart_api_fargate_log.name
  filter_pattern  = ""
  destination_arn = data.aws_kinesis_stream.stream.arn
  role_arn        = data.aws_iam_role.log_role.arn
}

resource "aws_cloudwatch_metric_alarm" "mart_api_prod_1_cpu_alarm" {
  count               = var.environment == "prod" && var.region == "us-east-1" ? 1 : 0
  alarm_name          = "mart-api-prod-1-cpu-usage-exceeds-90%"
  comparison_operator = "GreaterThanThreshold"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = 600
  statistic           = "Average"
  threshold           = 90
  alarm_description   = "Alarm when mart api prod-1 CPU usage exceeds 90%"
  alarm_actions       = ["arn:aws:sns:us-east-1:390844757890:InvApi-CloudWatch-Alerts"]
  ok_actions          = ["arn:aws:sns:us-east-1:390844757890:InvApi-CloudWatch-Alerts"]
  actions_enabled     = true
  evaluation_periods  = 1
  datapoints_to_alarm = 1

  dimensions = {
    ClusterName = "mart-service-cluster-prod"
    ServiceName = "mart-api-fargate-nlb-prod__1"
  }

  treat_missing_data = "missing"
}


resource "aws_cloudwatch_metric_alarm" "mart_api_prod_2_cpu_alarm" {
  count               = var.environment == "prod" && var.region == "us-east-1" ? 1 : 0
  alarm_name          = "mart-api-prod-2-cpu-usage-exceeds-90%"
  comparison_operator = "GreaterThanThreshold"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = 600
  statistic           = "Average"
  threshold           = 90
  alarm_description   = "Alarm when mart api prod-2 CPU usage exceeds 90%"
  alarm_actions       = ["arn:aws:sns:us-east-1:390844757890:InvApi-CloudWatch-Alerts"]
  ok_actions          = ["arn:aws:sns:us-east-1:390844757890:InvApi-CloudWatch-Alerts"]
  actions_enabled     = true
  evaluation_periods  = 1
  datapoints_to_alarm = 1

  dimensions = {
    ClusterName = "mart-service-cluster-prod"
    ServiceName = "mart-api-fargate-nlb-prod__2"
  }

  treat_missing_data = "missing"
}

resource "aws_cloudwatch_metric_alarm" "mart_api_prod_1_memory_alarm" {
  count               = var.environment == "prod" && var.region == "us-east-1" ? 1 : 0
  alarm_name          = "mart-api-prod-1-memory-usage-exceeds-95%"
  comparison_operator = "GreaterThanThreshold"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = 600
  statistic           = "Maximum"
  threshold           = 95
  alarm_description   = "Alarm when mart api prod-1 Memory usage exceeds 95%"
  alarm_actions       = ["arn:aws:sns:us-east-1:390844757890:InvApi-CloudWatch-Alerts"]
  ok_actions          = ["arn:aws:sns:us-east-1:390844757890:InvApi-CloudWatch-Alerts"]
  actions_enabled     = true
  evaluation_periods  = 1
  datapoints_to_alarm = 1

  dimensions = {
    ClusterName = "mart-service-cluster-prod"
    ServiceName = "mart-api-fargate-nlb-prod__1"
  }

  treat_missing_data = "missing"
}

resource "aws_cloudwatch_metric_alarm" "mart_api_prod_2_memory_alarm" {
  count               = var.environment == "prod" && var.region == "us-east-1" ? 1 : 0
  alarm_name          = "mart-api-prod-2-memory-usage-exceeds-95%"
  comparison_operator = "GreaterThanThreshold"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = 600
  statistic           = "Maximum"
  threshold           = 95
  alarm_description   = "Alarm when mart api prod-2 Memory usage exceeds 95%"
  alarm_actions       = ["arn:aws:sns:us-east-1:390844757890:InvApi-CloudWatch-Alerts"]
  ok_actions          = ["arn:aws:sns:us-east-1:390844757890:InvApi-CloudWatch-Alerts"]
  actions_enabled     = true
  evaluation_periods  = 1
  datapoints_to_alarm = 1

  dimensions = {
    ClusterName = "mart-service-cluster-prod"
    ServiceName = "mart-api-fargate-nlb-prod__2"
  }

  treat_missing_data = "missing"
}