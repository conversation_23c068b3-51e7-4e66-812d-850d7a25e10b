region                = "eu-west-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "stg"
environmentForTagging = "stg"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

zone_id                    = "dpd8eb6b.easn.morningstar.com"
has_mart_api_vpce_service  = 1
mart_api_vpce_service_name = "com.amazonaws.vpce.eu-west-1.vpce-svc-03ef16bd8deb48d1d"
vpce_subnet_ids = [
  "subnet-05833c296327b5241", # eu-west-1a-private
  "subnet-0c7ad2143f5e9a8dd"  #	eu-west-1b-private
]