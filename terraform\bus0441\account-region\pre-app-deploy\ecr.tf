module "velo-entitlement-api-ecr" {
  source          = "./../../../modules/ecr_repo"
  count           = local.is_dr ? 0 : 1
  repository_name = "entitlement-api-ecr"
  create_in_dr    = true
  default_tag_list = merge(local.tags,
    {
      SERVICEID = "ts01894"
  })
  providers = {
    aws.dr = aws.dr
  }
}

module "mart-api-ecr" {
  source          = "./../../../modules/ecr_repo"
  count           = local.is_dr ? 0 : 1
  repository_name = "mart-api-ecr"
  create_in_dr    = true
  default_tag_list = merge(local.tags,
    {
      SERVICEID = "ts01695"
  })
  providers = {
    aws.dr = aws.dr
  }
}

module "async-mart-api-ecr" {
  source            = "./../../../modules/ecr_repo"
  count             = local.is_dr ? 0 : 1
  repository_name   = "async-mart-api-ecr"
  create_in_dr      = true
  default_tag_list  = merge(local.tags,
    {
      SERVICEID   = "ts01695"
    })
  providers = {
    aws.dr = aws.dr
  }
}

module "ph-async-mart-api-ecr" {
  source            = "./../../../modules/ecr_repo"
  count             = local.is_dr ? 0 : 1
  repository_name   = "ph-async-mart-api-ecr"
  create_in_dr      = true
  default_tag_list  = merge(local.tags,
    {
      SERVICEID   = "ts01695"
    })
  providers = {
    aws.dr = aws.dr
  }
}

module "ph-mart-api-ecr" {
  source            = "./../../../modules/ecr_repo"
  count             = local.is_dr ? 0 : 1
  repository_name   = "ph-mart-api-ecr"
  create_in_dr      = true
  default_tag_list  = merge(local.tags,
    {
      SERVICEID   = "ts01695"
    })
  providers = {
    aws.dr = aws.dr
  }
}

module "mart-api-lambda-ecr" {
  source            = "./../../../modules/ecr_repo"
  count             = local.is_dr ? 0 : 1
  repository_name   = "mart-api-lambda-ecr"
  create_in_dr      = true
  default_tag_list  = merge(local.tags,
    {
      SERVICEID   = "ts01695"
    })
  providers = {
    aws.dr = aws.dr
  }
}
