#
#Configuration for lambda function: release-management-tool
#
# cloud watch logging config
resource "aws_cloudwatch_log_group" "release-management-tool-log" {
  count             = var.release_management_lambda_count
  name              = "/aws/lambda/release-management-tool-${var.environment}"
  retention_in_days = 7
  tags              = local.tags
}
#
#Configuration for lambda function: entitlement-sync-uim-email-to-user
#
# cloud watch config
resource "aws_cloudwatch_log_group" "entitlement-sync-uim-email-to-user-sns-log" {
  name              = "/aws/lambda/velo-entitlement-sync-email-info-from-UIM-${var.environment}"
  retention_in_days = 7
  tags              = local.tags
}

#
#Configuration for lambda function: entitlement-sync-ams-users
#
# cloud watch logging config
resource "aws_cloudwatch_log_group" "entitlement-sync-ams-users-log" {
  name              = "/aws/lambda/entitlement-sync-ams-users-${var.environment}"
  retention_in_days = 7
  tags              = local.tags
}
# create cloud watch event rule
resource "aws_cloudwatch_event_rule" "every_day_at_one_am_cst" {
  name = "every-day-at-1am-cst-${var.environment}-${var.region}"
  description = "Triggers every day at 1:00 AM CST"
  schedule_expression = "cron(0 6 * * ? *)"
}
# create cloud watch event target
resource "aws_cloudwatch_event_target" "entitlement_sync_ams_users_at_one_am_cst" {
  rule = aws_cloudwatch_event_rule.every_day_at_one_am_cst.name
  target_id = "entitlement-sync-ams-users-lambda"
  arn = aws_lambda_function.entitlement-sync-ams-users-lambda.arn
}

#
#Configuration for lambda function: entitlement-sync-data-group-permission
#
# cloud watch logging config
resource "aws_cloudwatch_log_group" "entitlement-sync-data-group-permission-log" {
  name              = "/aws/lambda/entitlement-sync-data-group-permission-${var.environment}"
  retention_in_days = 7
  tags              = local.tags
}
# create cloud watch event rule
resource "aws_cloudwatch_event_rule" "every_day_at_two_am_cst" {
  name = "every-day-at-2am-cst-${var.environment}-${var.region}"
  description = "Triggers every day at 2:00 AM CST"
  schedule_expression = "cron(0 7 * * ? *)"
}
# create cloud watch event target
resource "aws_cloudwatch_event_target" "entitlement_sync_data_group_permission_at_two_am_cst" {
  rule = aws_cloudwatch_event_rule.every_day_at_two_am_cst.name
  target_id = "entitlement-sync-data-group-permission-lambda"
  arn = aws_lambda_function.entitlement-sync-data-group-permission-lambda.arn
}

#
#Configuration for lambda function: entitlement-sync-private-index-data
#
# cloud watch logging config
resource "aws_cloudwatch_log_group" "entitlement-sync-private-index-data-log" {
  name              = "/aws/lambda/entitlement-sync-private-index-data-${var.environment}"
  retention_in_days = 7
  tags              = local.tags
}
# create cloud watch event rule
resource "aws_cloudwatch_event_rule" "every_day_at_three_am_cst" {
  name = "every-day-at-3am-cst-${var.environment}-${var.region}"
  description = "Triggers every day at 3:00 AM CST"
  schedule_expression = "cron(0 8 * * ? *)"
}

# create cloud watch event target
resource "aws_cloudwatch_event_target" "entitlement-sync-private-index-data_at_three_am_cst" {
  rule = aws_cloudwatch_event_rule.every_day_at_three_am_cst.name
  target_id = "entitlement-sync-private-index-data-lambda"
  arn = aws_lambda_function.entitlement-sync-private-index-data-lambda.arn
}

#
#Configuration for lambda function: entitlement-sync-salesforce-data
#
# cloud watch logging config
resource "aws_cloudwatch_log_group" "entitlement-sync-salesforce-data-log" {
  name              = "/aws/lambda/entitlement-sync-salesforce-data-${var.environment}"
  retention_in_days = 7
  tags              = local.tags
}
# create cloud watch event rule
resource "aws_cloudwatch_event_rule" "every_day_every_two_hours" {
  name = "every-day-every-two-hours-${var.environment}-${var.region}"
  description = "Triggers every day every two hours"
  schedule_expression = "cron(0 */2 * * ? *)"
}
# create cloud watch event target
resource "aws_cloudwatch_event_target" "entitlement-sync-salesforce-data_every_two_hours" {
  rule = aws_cloudwatch_event_rule.every_day_every_two_hours.name
  target_id = "entitlement-sync-salesforce-data-lambda"
  arn = aws_lambda_function.entitlement-sync-salesforce-data-lambda.arn
}

#
#Configuration for lambda function: entitlement-sync-inactive-user-permission
#
# cloud watch logging config
resource "aws_cloudwatch_log_group" "entitlement-sync-inactive-user-permission-log" {
  name              = "/aws/lambda/entitlement-sync-inactive-user-permission-${var.environment}"
  retention_in_days = 7
  tags              = local.tags
}
# create cloud watch event rule
resource "aws_cloudwatch_event_rule" "every_day_at_four_am_cst" {
  name = "every-day-at-4am-cst-${var.environment}-${var.region}"
  description = "Triggers every day at 4:00 AM CST"
  schedule_expression = "cron(0 9 * * ? *)"
}
# create cloud watch event target
resource "aws_cloudwatch_event_target" "entitlement-sync-inactive-user-permission_at_four_am_cst" {
  rule = aws_cloudwatch_event_rule.every_day_at_four_am_cst.name
  target_id = "entitlement-sync-inactive-user-permission-lambda"
  arn = aws_lambda_function.entitlement-sync-inactive-user-permission-lambda.arn
}

# create cloud watch event rule
resource "aws_cloudwatch_event_rule" "every_day_every_four_hour_ams_users" {
  name                = "every-day-every-four-hour-${var.environment}-${var.region}-ams-users"
  description         = "Triggers every day every four hour (00:00, 04:00, 08:00, 12:00, 16:00, and 20:00 hours)"
  schedule_expression = "cron(0 */4 * * ? *)"
}

# create cloud watch event target
resource "aws_cloudwatch_event_target" "entitlement_sync_ams_users_every_four_hours" {
  rule = aws_cloudwatch_event_rule.every_day_every_four_hour_ams_users.name
  target_id = "entitlement-sync-ams-users-lambda"
  arn = aws_lambda_function.entitlement-sync-ams-users-lambda.arn
}