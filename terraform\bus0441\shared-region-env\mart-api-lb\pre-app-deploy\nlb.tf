resource "aws_lb" "network_lb" {
  count                            = var.required
  name                             = var.mart_api_nlb_name
  internal                         = true
  load_balancer_type               = "network"
  enable_deletion_protection       = false
  enable_cross_zone_load_balancing = true

  subnets = [
    "${module.region_data.private_subnet_1_id[0]}",
    "${module.region_data.private_subnet_2_id[0]}",
    "${module.region_data.private_subnet_3_id[0]}"
  ]

  idle_timeout = 300
  tags         = local.tags
}

resource "aws_lb_listener" "network_lb_listener_443" {
  count             = var.required
  load_balancer_arn = aws_lb.network_lb[0].arn
  port              = 443
  protocol          = "TCP"

  default_action {
    target_group_arn = aws_lb_target_group.mart_api_alb_target_group[0].arn
    type             = "forward"
  }
  tags = local.tags
}

resource "aws_lb_listener" "network_lb_listener_80" {
  count             = var.required
  load_balancer_arn = aws_lb.network_lb[0].arn
  port              = 80
  protocol          = "TCP"

  default_action {
    target_group_arn = aws_lb_target_group.mart_api_alb_target_group_80[0].arn
    type             = "forward"
  }
  tags = local.tags
}

resource "aws_lb_target_group" "mart_api_nlb_fargate_target_group" {
  name        = "mart-api-tg-nlb-${var.environment}"
  target_type = "ip"
  port        = 8080
  protocol    = "TCP"
  vpc_id      = var.vpc

  health_check {
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTP"
    matcher             = "200-399"
    timeout             = "3"
    path                = "/investment-api/v1/health-check"
    unhealthy_threshold = "3"
  }
  tags = local.tags
}

# TODO Need to Import Resource (source: mart-api/pre-app-deploy/nlb.tf)
resource "aws_lb_target_group" "mart_api_alb_target_group" {
  count       = var.required
  name        = var.environment == "prod" ? "mart-api-tg-alb-prod" : "mart-api-tg-alb-nonprod"
  target_type = "alb"
  port        = 443
  protocol    = "TCP"
  vpc_id      = var.vpc

  health_check {
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTPS"
    matcher             = "200-399"
    timeout             = "3"
    path                = "/investment-api/v1/health-check"
    unhealthy_threshold = "3"
  }
  tags = local.tags
}

# TODO Need to Import Resource (source: mart-api/pre-app-deploy/nlb.tf)
resource "aws_lb_target_group" "mart_api_alb_target_group_80" {
  count       = var.required
  name        = var.environment == "prod" ? "mart-api-tg-alb-80-prod" : "mart-api-tg-alb-80-nonprod"
  target_type = "alb"
  port        = 80
  protocol    = "TCP"
  vpc_id      = var.vpc

  health_check {
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTP"
    matcher             = "200-399"
    timeout             = "3"
    path                = "/investment-api/v1/health-check"
    unhealthy_threshold = "3"
  }
  tags = local.tags
}

resource "aws_lb_target_group_attachment" "mart_api_alb_target_group_attachment" {
  count            = var.required
  target_group_arn = aws_lb_target_group.mart_api_alb_target_group[0].arn
  target_id        = aws_lb.mart_api_alb.arn
  port             = 443
}

resource "aws_lb_target_group_attachment" "mart_api_alb_target_group_attachment_80" {
  count            = var.required
  target_group_arn = aws_lb_target_group.mart_api_alb_target_group_80[0].arn
  target_id        = aws_lb.mart_api_alb.arn
  port             = 80
}

# TODO Need to Import Resource (source: mart-api/pre-app-deploy/nlb.tf)
resource "aws_lb_listener" "tcp80" {
  count             = var.required
  load_balancer_arn = aws_lb.network_lb[0].arn
  port              = 80
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.mart_api_alb_target_group_80[0].arn
  }
}

# TODO Need to Import Resource (source: mart-api/pre-app-deploy/nlb.tf)
resource "aws_lb_listener" "tcp443" {
  count             = var.required
  load_balancer_arn = aws_lb.network_lb[0].arn
  port              = 443
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.mart_api_alb_target_group[0].arn
  }
}

resource "aws_lb_listener" "tls8443" {
  count             = var.required == 1 && var.mart-api-tg-nlb-dev-arn != "" ? 1 : 0
  load_balancer_arn = aws_lb.network_lb[0].arn
  port              = 8443
  protocol          = "TLS"
  ssl_policy        = "ELBSecurityPolicy-TLS-1-2-2017-01"
  certificate_arn   = data.aws_acm_certificate.mart_acm_certificate.arn

  default_action {
    type             = "forward"
    target_group_arn = var.mart-api-tg-nlb-dev-arn
  }
  tags = local.tags
}

resource "aws_lb_listener" "tcp8080" {
  count             = var.required == 1 && var.mart-api-tg-nlb-qa-arn != "" ? 1 : 0
  load_balancer_arn = aws_lb.network_lb[0].arn
  port              = 8080
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = var.mart-api-tg-nlb-qa-arn
  }
  tags = local.tags
}
