resource "harness_platform_triggers" "push_to_master" {
  count       = var.triggers.push_to_master ? 1 : 0
  identifier  = "push_to_master"
  name        = "Push-to-master"
  org_id      = var.org_id
  project_id  = var.project_id
  target_id   = var.pipeline_id
  yaml        = templatefile("${path.module}/templates/triggers/push_branch_trigger.tftpl",
    {
      org_id        = var.org_id
      project_id    = var.project_id
      pipeline_id   = var.pipeline_id
      repo_name     = var.repo_name
      trigger_id    = "push_to_master"
      trigger_name  = "Push-to-master"
      operator      = "Equals"
      branch        = "master"
      input_set_id  = "push_to_input_set"
    }
  )
  depends_on = [harness_platform_input_set.this]
}

resource "harness_platform_triggers" "push_to_develop" {
  count       = var.triggers.push_to_develop ? 1 : 0
  identifier  = "push_to_develop"
  name        = "Push-to-develop"
  org_id      = var.org_id
  project_id  = var.project_id
  target_id   = var.pipeline_id
  yaml        = templatefile("${path.module}/templates/triggers/push_branch_trigger.tftpl",
    {
      org_id        = var.org_id
      project_id    = var.project_id
      pipeline_id   = var.pipeline_id
      repo_name     = var.repo_name
      trigger_id    = "push_to_develop"
      trigger_name  = "Push-to-develop"
      operator      = "Equals"
      branch        = "develop"
      input_set_id  = "push_to_input_set"
    }
  )
  depends_on = [harness_platform_input_set.this]
}
