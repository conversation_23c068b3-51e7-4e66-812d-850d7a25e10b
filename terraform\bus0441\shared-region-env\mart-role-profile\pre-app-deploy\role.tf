resource "aws_iam_instance_profile" "mart_role_profile" {
  count = var.required
  path  = "/"
  name  = "mart-role-instance-profile-${var.region}-${var.environment}"
  role  = "mart-role-${var.environment}"
}

resource "aws_iam_role" "mart_role" {
  count              = var.required
  name               = "mart-role-${var.environment}"
  assume_role_policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Action": "sts:AssumeRole",
            "Principal": {
                "Service": [
                    "ec2.amazonaws.com",
                    "es.amazonaws.com",
                    "s3.amazonaws.com",
                    "batchoperations.s3.amazonaws.com",
                    "ecs-tasks.amazonaws.com",
                    "batch.amazonaws.com",
                    "events.amazonaws.com",
                    "ecs.amazonaws.com",
                    "lambda.amazonaws.com",
                    "apigateway.amazonaws.com",
                    "ecs.application-autoscaling.amazonaws.com",
                    "ses.amazonaws.com"
                ],
                "AWS": [
                    "${var.velo_role}",
                    "arn:aws:iam::${var.account_id}:root",
                    "${var.asyncdata_feed_worker}",
                    "${var.asyncdata_feed_worker_dr}",
                    "${var.velo_test_role}",
                    "${var.dataac_lambda_role}"
                ]
            },
            "Effect": "Allow"
        }
    ]
}
  EOF
}

resource "aws_iam_role_policy_attachment" "s3_full" {
  count      = var.required
  role       = aws_iam_role.mart_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/AmazonS3FullAccess"
}

resource "aws_iam_role_policy_attachment" "batch_service" {
  count      = var.required
  role       = aws_iam_role.mart_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSBatchServiceRole"
}

resource "aws_iam_role_policy_attachment" "batch_full" {
  count      = var.required
  role       = aws_iam_role.mart_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/AWSBatchFullAccess"
}

resource "aws_iam_role_policy_attachment" "ec2_container_service" {
  count      = var.required
  role       = aws_iam_role.mart_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

resource "aws_iam_role_policy_attachment" "secrets_manager_read_write" {
  count      = var.required
  role       = aws_iam_role.mart_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/SecretsManagerReadWrite"
}

resource "aws_iam_role_policy_attachment" "auto_scale" {
  count      = var.required
  role       = aws_iam_role.mart_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/AutoScalingFullAccess"
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution" {
  count      = var.required
  role       = aws_iam_role.mart_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_iam_role_policy" "mart-lambda-role-policy" {
  count  = var.required
  name   = "velo-lambda-role-${var.environment}"
  role   = aws_iam_role.mart_role[0].id
  policy = <<POLICY
{
	"Statement": [{
			"Action": [
				"logs:CreateLogGroup",
				"logs:CreateLogStream",
				"logs:PutLogEvents",
				"cloudwatch:putMetricData",
				"ec2:CreateNetworkInterface",
				"ec2:DescribeNetworkInterfaces",
				"ec2:DeleteNetworkInterface",
				"SNS:Publish",
				"SNS:Subscribe",
        "ses:SendEmail",
				"xray:BatchGetTraces",
				"xray:GetServiceGraph",
				"xray:GetTraceGraph",
				"xray:GetTraceSummaries",
                "es:UpdateElasticsearchDomainConfig",
				"lambda:*",
				"kms:GenerateDataKey",
                "kms:Decrypt"
			],
			"Effect": "Allow",
			"Resource": "*"
		}
	]
}
    POLICY
}

resource "aws_iam_role_policy" "mart-athena-role-policy" {
  count  = var.required
  name   = "mart-athena-role-${var.environment}"
  role   = aws_iam_role.mart_role[0].id
  policy = <<POLICY
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "VisualEditor0",
            "Effect": "Allow",
            "Action": [
                "athena:*",
                "glue:*",
                "glue:SearchTables",
                "athena:StartQueryExecution",
                "glue:GetConnections",
                "glue:DeleteDatabase",
                "glue:GetTableVersions",
                "glue:GetPartitions",
                "glue:UpdateTable",
                "athena:GetQueryResults",
                "glue:DeleteTable",
                "athena:GetDatabase",
                "s3:ListBucket",
                "athena:DeleteNamedQuery",
                "glue:DeleteWorkflow",
                "glue:StartWorkflowRun",
                "lakeformation:*",
                "glue:ListWorkflows",
                "iam:GetRole",
                "cloudtrail:LookupEvents",
                "glue:UpdateDatabase",
                "glue:CreateTable",
                "glue:GetTables",
                "iam:ListRoles",
                "glue:GetDatabases",
                "s3:GetBucketAcl",
                "cloudtrail:DescribeTrails",
                "glue:GetTable",
                "glue:GetDatabase",
                "s3:ListAllMyBuckets",
                "glue:BatchGetWorkflows",
                "glue:CreateDatabase",
                "athena:GetQueryExecution",
                "iam:ListUsers",
                "glue:GetWorkflowRuns",
                "iam:GetRolePolicy",
                "s3:GetBucketLocation",
                "glue:GetWorkflow"
            ],
            "Resource": "*"
        },
        {
            "Sid": "VisualEditor1",
            "Effect": "Deny",
            "Action": "lakeformation:PutDataLakeSettings",
            "Resource": "*"
        }
    ]
}
    POLICY
}

resource "aws_iam_role_policy" "mart-ec2-role-policy" {
  count  = var.required
  name   = "mart-ec2-access-role-${var.environment}"
  role   = aws_iam_role.mart_role[0].id
  policy = <<POLICY
{
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "autoscaling:Describe*",
                "autoscaling:UpdateAutoScalingGroup",
                "cloudformation:CreateStack",
                "cloudformation:DeleteStack",
                "cloudformation:DescribeStack*",
                "cloudformation:UpdateStack",
                "cloudwatch:GetMetricStatistics",
                "ec2:Describe*",
                "elasticloadbalancing:*",
                "ecs:*",
                "events:DescribeRule",
                "events:DeleteRule",
                "events:ListRuleNamesByTarget",
                "events:ListTargetsByRule",
                "events:PutRule",
                "events:PutTargets",
                "events:RemoveTargets",
                "iam:ListInstanceProfiles",
                "iam:ListRoles",
                "iam:PassRole",
                "SSM:Get*"
            ],
            "Resource": "*"
        }
    ]
}
    POLICY
}

resource "aws_iam_role_policy" "mart-snapshot-role-policy" {
  count  = var.required
  name   = "mart-snapshot-policy-${var.environment}"
  role   = aws_iam_role.mart_role[0].id
  policy = <<POLICY
{
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "es:ESHttpPost",
                "es:ESHttpGet",
                "es:ESHttpDelete",
                "es:ESHttpPut",
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents",
                "elasticache:*"
            ],
            "Resource": "*"
        }
    ]
}
    POLICY
}

resource "aws_iam_role_policy" "mart-redshift-role-policy" {
  count  = var.required
  name   = "mart-redshift-role-${var.environment}"
  role   = aws_iam_role.mart_role[0].id
  policy = <<POLICY
{
    "Statement": [
        {
            "Effect": "Allow",
            "Action": "sts:AssumeRole",
            "Resource": [
                "${var.redshift_assume_role}"
            ]
        }
    ]
}
    POLICY
}

resource "aws_iam_role_policy" "mart-dynamodb-full-access-policy" {
  count  = var.required
  name   = "mart-dynamodb-role-${var.environment}"
  role   = aws_iam_role.mart_role[0].id
  policy = <<POLICY
{
    "Statement": [
        {
            "Effect": "Allow",
            "Action": "dynamodb:*",
            "Resource": "*"
        }
    ]
}
    POLICY
}

resource "aws_iam_role_policy" "mart-sqs-access-policy" {
  count  = var.required
  name   = "mart-sqs-role-${var.environment}"
  role   = aws_iam_role.mart_role[0].id
  policy = <<POLICY
{
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "sqs:ListQueues",
                "sqs:SendMessage",
                "sqs:ReceiveMessage",
                "sqs:DeleteMessage",
                "sqs:GetQueueAttributes",
                "sqs:GetQueueUrl",
                "sqs:ChangeMessageVisibility"
            ],
            "Resource": "*"
        }
    ]
}
    POLICY
}
