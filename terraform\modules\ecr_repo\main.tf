resource "aws_ecr_repository" "ecr_repo" {
  name                 = var.repository_name
  image_tag_mutability = "MUTABLE"
  tags                 = var.default_tag_list

  image_scanning_configuration {
    scan_on_push = var.scan_on_push
  }
}

resource "aws_ecr_lifecycle_policy" "ecr_policy" {
  repository  = aws_ecr_repository.ecr_repo.name
  depends_on  = [
    aws_ecr_repository.ecr_repo
  ]

  policy = <<EOF
  {
    "rules": [
      {
        "rulePriority": 2,
        "description": "Keep last 15 images",
        "selection": {
          "tagStatus": "any",
          "countType": "imageCountMoreThan",
          "countNumber": 15
        },
        "action": {
          "type": "expire"
        }
      },
      {
        "rulePriority": 1,
        "description": "Remove untagged images",
        "selection": {
          "tagStatus": "untagged",
          "countType": "imageCountMoreThan",
          "countNumber": 1
        },
        "action": {
          "type": "expire"
        }
      }
    ]
  }
  EOF
}

resource "aws_ecr_repository" "ecr_repo_dr" {
  count                 = var.create_in_dr ? 1 : 0
  provider              = aws.dr
  name                  = var.repository_name
  image_tag_mutability  = "MUTABLE"
  tags                  = var.default_tag_list

  image_scanning_configuration {
    scan_on_push = var.scan_on_push
  }
}

resource "aws_ecr_lifecycle_policy" "ecr_policy_dr" {
  count       = var.create_in_dr ? 1 : 0
  repository  = aws_ecr_repository.ecr_repo_dr[0].name
  depends_on  = [
    aws_ecr_repository.ecr_repo_dr
  ]

  policy = <<EOF
  {
    "rules": [
      {
        "rulePriority": 2,
        "description": "Keep last 15 images",
        "selection": {
          "tagStatus": "any",
          "countType": "imageCountMoreThan",
          "countNumber": 15
        },
        "action": {
          "type": "expire"
        }
      },
      {
        "rulePriority": 1,
        "description": "Remove untagged images",
        "selection": {
          "tagStatus": "untagged",
          "countType": "imageCountMoreThan",
          "countNumber": 1
        },
        "action": {
          "type": "expire"
        }
      }
    ]
  }
  EOF
}