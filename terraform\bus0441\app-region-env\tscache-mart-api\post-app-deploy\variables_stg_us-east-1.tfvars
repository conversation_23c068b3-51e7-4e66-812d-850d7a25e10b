region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "stg"
environmentForTagging = "stg"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

task_desired_count = "1"
peak_desired_count = "2"
zone_id            = "dpd8eb6b.easn.morningstar.com"
mart_api_nlb_name  = "mart-api-nlb-stg"
required           = 1
domain_name        = "*.dpd8eb6b.easn.morningstar.com"
weight             = 1