#########################################################################################
## Resources now being handled using Cloud formation templates located in the aws folder
#########################################################################################
#
# // tscache_mart_api_fargate_nlb_service this cluster is deployed by harness pipeline task definition
# // instead of being managed by terraform
# resource "aws_cloudwatch_metric_alarm" "tscache_mart_api_fargate_nlb_cpu_utilization_high" {
#   alarm_name          = "tscache-mart-api-fargate-nlb-cpu-high-${var.environment}"
#   comparison_operator = "GreaterThanOrEqualToThreshold"
#   evaluation_periods  = 1
#   metric_name         = "CPUUtilization"
#   namespace           = "AWS/ECS"
#   period              = "60"
#   statistic           = "Average"
#   threshold           = var.ecs_nlb_cpu_high_threshold
#
#   dimensions = {
#     ClusterName = data.aws_ecs_cluster.mart-cluster.cluster_name
#     ServiceName = data.aws_ecs_service.tscache_mart_api_fargate_nlb_service.service_name
#   }
#
#   alarm_actions = [aws_appautoscaling_policy.tscache_mart_api_fargate_nlb_up.arn]
# }
#
# resource "aws_cloudwatch_metric_alarm" "tscache_mart_api_fargate_nlb_cpu_utilization_low" {
#   alarm_name          = "tscache-mart-api-fargate-nlb-cpu-low-${var.environment}"
#   comparison_operator = "LessThanThreshold"
#   evaluation_periods  = 1
#   metric_name         = "CPUUtilization"
#   namespace           = "AWS/ECS"
#   period              = "60"
#   statistic           = "Maximum"
#   threshold           = var.ecs_nlb_cpu_low_threshold
#
#   dimensions = {
#     ClusterName = data.aws_ecs_cluster.mart-cluster.cluster_name
#     ServiceName = data.aws_ecs_service.tscache_mart_api_fargate_nlb_service.service_name
#   }
#
#   alarm_actions = [aws_appautoscaling_policy.tscache_mart_api_fargate_nlb_down.arn]
# }