resource "aws_dynamodb_table" "async_martapi_history_us_east_1" {
  count            = var.required_replica
  provider         = aws.main
  hash_key         = "job_id"
  name             = "async-martapi-history-${var.environment}"
  billing_mode     = "PAY_PER_REQUEST"
  stream_enabled   = true
  stream_view_type = "NEW_AND_OLD_IMAGES"

  attribute {
    name = "job_id"
    type = "S"
  }

  lifecycle {
    ignore_changes = [replica]
  }
}

resource "aws_dynamodb_table_replica" "async_martapi_history_eu_west_1" {
  count            = var.environment == "qa" ? 0 : var.required_replica
  provider         = aws.alt
  global_table_arn = aws_dynamodb_table.async_martapi_history_us_east_1[0].arn
}