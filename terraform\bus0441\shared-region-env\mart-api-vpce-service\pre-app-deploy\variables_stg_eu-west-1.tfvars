region                = "eu-west-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "stg"
environmentForTagging = "stg"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

mart_api_nlb_arn = "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/net/mart-api-nlb-stg/66bdec2c2938b49d"
has_vpce_service = true
vpce_service_allowed_principals = [
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::021033229982:root"
]
vpce_subnet_ids = [
  "subnet-05833c296327b5241", # eu-west-1a-private
  "subnet-0c7ad2143f5e9a8dd"  # eu-west-1b-private
]
