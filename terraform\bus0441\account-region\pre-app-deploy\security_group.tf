resource "aws_security_group" "private_mongo" {
  name        = "private-mongo-client"
  description = "Security group for clients connecting to MongoDB"
  vpc_id      = data.aws_vpc.dpda_account.id


  egress {
    description = "Allow outbound MongoDB traffic"
    from_port   = 27017
    to_port     = 27017
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "private-mongo-client"
  }
}
