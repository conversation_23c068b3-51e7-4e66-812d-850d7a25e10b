region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

zone_id                       = "dpd6b927.eas.morningstar.com"
has_custom_calc_vpce          = 1
custom_calc_vpce_service_name = "com.amazonaws.vpce.us-east-1.vpce-svc-0e4b194f3e2750ae1"
has_mart_api_vpce_service     = 1
mart_api_vpce_service_name    = "com.amazonaws.vpce.us-east-1.vpce-svc-047c9326d71c659fa"
required_rdb_vpce             = 1
rdb_vpce_name                 = "RDB-ReadOnly"
rdb_vpce_service_name         = "com.amazonaws.vpce.us-east-1.vpce-svc-04ffa54f83b6be15a"
vpce_subnet_ids = [
  "subnet-0c3db8d6d567a4f4a", # us-east-1a-private
  "subnet-0d610a03d59c8889d"  #	us-east-1b-private
]

