provider "aws" {
  region = var.region
  assume_role {
    role_arn = var.use_role_to_assume ? var.role_to_assume : null
    session_name = var.session_name
  }
  default_tags {
    tags = local.tags
  }
}

provider "aws" {
  region = var.dr_region
  alias  = "replica"

  assume_role {
    role_arn     = var.use_role_to_assume ? var.role_to_assume : null
    session_name = "${var.session_name}_DR"
  }
  default_tags {
    tags = local.tags
  }
}

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.30.0"
    }
  }
  backend "s3" {}
}