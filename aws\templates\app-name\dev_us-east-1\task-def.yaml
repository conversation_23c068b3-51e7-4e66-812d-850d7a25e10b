family: mart-api-fargate-nlb-task-definition-dev
executionRoleArn: arn:aws:iam::<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:role/mart-role-dev
taskRoleArn: arn:aws:iam::<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:role/mart-role-dev
containerDefinitions:
  - name: mart-api-fargate-container-dev
    image: <+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/[ECR_REPO_PATH]:<+serviceVariables.ImageTag>
    cpu: 2048
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: dev
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/mart-api-fargate-dev
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs
    secrets:
      - name: SPRING_DATASOURCE_RDB_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:parameter/MART/DATAPI_READER_STG"
      - name: MARTGATEWAY_REDSHIFT_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:parameter/LAKEHOUSE/ADMIN_PASSWORD"
      - name: MARTGATEWAY_EOD_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:parameter/MART/MARKET_DB_PASSWORD"  

cpu: '2048'
memory: '4096'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>