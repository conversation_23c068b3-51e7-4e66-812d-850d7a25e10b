region                = "eu-west-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "stg"
environmentForTagging = "stg"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

config_trigger_url       = "http://martapi-stg-eu-west-1.dpd8eb6b.easn.morningstar.com/v1/data-point/publish"
required                 = 0
rdb_topic_sqs            = "arn:aws:sns:us-east-1:************:dbops-rdb-flattablestatus"
extension_arn            = "arn:aws:lambda:eu-west-1:************:layer:AWS-Parameters-and-Secrets-Lambda-Extension:11"
bucket_for_config_update = "mart-data-stg-eu"
dataac_account_id        = "************"