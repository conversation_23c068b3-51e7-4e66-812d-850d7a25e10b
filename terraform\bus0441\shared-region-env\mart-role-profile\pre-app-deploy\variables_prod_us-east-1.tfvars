region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

required                 = 1
account_id               = "************"
velo_role                = "arn:aws:iam::************:role/velo-stand-alone-prod"
redshift_assume_role     = "arn:aws:iam::************:role/cz_r_************_mart-role-prod"
asyncdata_feed_worker    = "arn:aws:iam::************:role/asyncdata/ecs-asyncdata-feed-worker"
asyncdata_feed_worker_dr = "arn:aws:iam::************:role/asyncdata/dr-ecs-asyncdata-feed-worker"
velo_test_role           = "arn:aws:iam::************:role/velo-ecs-role-qatest"
dataac_lambda_role       = "arn:aws:iam::************:role/lambda-execution-role"