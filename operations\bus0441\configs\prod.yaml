deploy_role_name: mstar-engr-cross-account-deploy

# SSM Parameters
ssm:
  source_region: us-east-1
  sync_tag_key: SyncAcrossRegions

# Elasticache Configuration
elasticache:
  - template:
      location: 'templates/elasticache.json.j2'
      variables:
        target_cluster_name: mart-data-v7-cluster-prod-dr
        target_cluster_description: Mart Data Cache
        node_group_configuration:
          - Slots: 0-2429,4144-5461,5731-6078,12359-12785,13801-14739
            ReplicaCount: 1
          - Slots: 2430-3928,3930-4143,6079-6492,6827-6916,7736-10922,12824-12880
            ReplicaCount: 1
          - Slots: 3929,5462-5730,6493-6826,6917-7735,10923-12358,12786-12823,12881-13800,14740-16383
            ReplicaCount: 1
        cache_parameter_group: default.redis7.cluster.on
        engine_version: "7.0"
        nodenumber: 3
        cache_node_type: cache.r5.xlarge
        need_data_backup: true
        data_backup_prefix: redis-data-snapshot/automatic.mart-data-v7-cluster-prod
        s3_bucket_name: mart-data-prod-eu
        s3_bucket_region: eu-west-1
        security_group_ids:
          - sg-0a570b98b80bc8079
          - sg-012adae20d841f7a2
    route53:
      template:
        location: 'templates/route53/elasticache.json.j2'
        variables:
          zone_id: ZPHK6BJ6MT62F
          identifier: prod-dr
          weight: 1
          domain_name: mart-data-v7-cluster-prod-dr-eu-west-1.dat688b3.eas.morningstar.com
  - template:
      location: 'templates/elasticache.json.j2'
      variables:
        target_cluster_name: application-cache-v7-cluster-prod-dr
        target_cluster_description: mart application cache
        node_group_configuration:
          - Slots: 0-4277,7008-8191
          - Slots: 8192-10883,13615-16383
          - Slots: 4278-7007,10884-13614
        cache_parameter_group: default.redis7.cluster.on
        engine_version: "7.0"
        nodenumber: 3
        need_data_backup: false
        cache_node_type: cache.r7g.xlarge
        security_group_ids:
          - sg-0a570b98b80bc8079
          - sg-012adae20d841f7a2
    route53:
      template:
        location: 'templates/route53/elasticache.json.j2'
        variables:
          zone_id: ZPHK6BJ6MT62F
          identifier: prod-dr
          weight: 0
          domain_name: application-cache-v7-cluster-prod-dr-eu-west-1.dat688b3.eas.morningstar.com
  - template:
      location: 'templates/elasticache.json.j2'
      variables:
        target_cluster_name: ts-application-cache-v7-cluster-prod-dr
        target_cluster_description: redis cache cluster for TS data
        node_group_configuration:
          - Slots: 0-8191
          - Slots: 8192-16383
        cache_parameter_group: default.redis7.cluster.on
        engine_version: "7.1"
        nodenumber: 2
        need_data_backup: false
        cache_node_type: cache.r7g.xlarge
        security_group_ids:
          - sg-0a570b98b80bc8079
          - sg-012adae20d841f7a2
    route53:
      template:
        location: 'templates/route53/elasticache.json.j2'
        variables:
          zone_id: ZPHK6BJ6MT62F
          identifier: prod-dr
          weight: 1
          domain_name: ts-application-cache-v7-cluster-prod-dr-eu-west-1.dat688b3.eas.morningstar.com