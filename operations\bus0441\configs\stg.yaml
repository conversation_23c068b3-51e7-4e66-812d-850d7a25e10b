deploy_role_name: mstar-engr-cross-account-deploy

# SSM Parameters
ssm:
  source_region: us-east-1
  sync_tag_key: SyncAcrossRegions

# Route53 Configuration
route53:
  zone_id: Z1763QDC3XWVS7
  records:
    
# Elasticache Configuration
elasticache:
  - template:
      location: 'templates/elasticache.json.j2'
      variables:
        target_cluster_name: mart-data-v7-cluster-stg-dr
        target_cluster_description: Mart Data Cache
        s3_bucket_name: mart-data-stg-eu
        target_region: eu-west-1
        node_group_configuration:
          - Slots: 0-6798, 14991-16383
          - Slots: 6799-14990
        cache_parameter_group: default.redis7.cluster.on
        engine_version: "7.0"
        nodenumber: 2
        cache_node_type: cache.r7g.xlarge
        need_data_backup: true
        data_backup_prefix: redis-data-snapshot/automatic.mart-data-v7-cluster-stg
        security_group_ids:
          - sg-0d7a36e9bd3e6b118 #private-web
          - sg-0136792b458dbbb96 #private_app
    route53:
      template:
        location: 'templates/route53/elasticache.json.j2'
        variables:
          zone_id: Z1763QDC3XWVS7
          identifier: stg-dr
          weight: 1
          domain_name: mart-data-v7-cluster-stg-dr-eu-west-1.date7ebe.easn.morningstar.com
  - template:
      location: 'templates/elasticache.json.j2'
      variables:
        target_region: eu-west-1
        target_cluster_name: application-cache-v7-cluster-stg-dr
        target_cluster_description: mart application cache
        node_group_configuration:
          - Slots: 5460-8191,8322-11051
          - Slots: 611-1073,11386-16383
          - Slots: 0-610,1074-5459,8192-8321,11052-11385
        cache_parameter_group: default.redis7.cluster.on
        engine_version: "7.0"
        nodenumber: 3
        need_data_backup: false
        cache_node_type: cache.t3.medium
        security_group_ids:
          - sg-0d7a36e9bd3e6b118
          - sg-0136792b458dbbb96
    route53:
      template:
        location: 'templates/route53/elasticache.json.j2'
        variables:
          zone_id: Z1763QDC3XWVS7
          identifier: stg-dr
          weight: 0
          domain_name: application-cache-v7-cluster-stg-dr-eu-west-1.date7ebe.easn.morningstar.com
  - template:
      location: 'templates/elasticache.json.j2'
      variables:
        target_region: eu-west-1
        target_cluster_name: ts-application-cache-v7-cluster-stg-dr
        target_cluster_description: redis cache cluster for TS data
        node_group_configuration:
          - Slots: 0-16383
        cache_parameter_group: default.redis7.cluster.on
        nodenumber: 1
        engine_version: "7.1"
        need_data_backup: false
        cache_node_type: cache.t3.medium
        security_group_ids:
          - sg-0d7a36e9bd3e6b118
          - sg-0136792b458dbbb96
    route53:
      template:
        location: 'templates/route53/elasticache.json.j2'
        variables:
          zone_id: Z1763QDC3XWVS7
          identifier: stg-dr
          weight: 1
          domain_name: ts-application-cache-v7-cluster-stg-dr-eu-west-1.date7ebe.easn.morningstar.com