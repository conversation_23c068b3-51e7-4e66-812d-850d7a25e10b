variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "has_custom_calc_vpce" {
  description = "flag to note whether we should create a VPC Endpoint for mart-api to custom calc API"
  default     = 0
}


variable "custom_calc_vpce_service_name" {
  type    = string
  default = null
}

variable "has_mart_api_vpce_service" {
  description = "flag to note whether we should create a VPC Endpoint Service for mart-api in this env"
  default     = 0
}

variable "mart_api_vpce_service_name" {
  type    = string
  default = null
}

variable "vpce_subnet_ids" {
  type    = list(string)
  default = []
}

variable "rdb_vpce_service_name" {
  type    = string
  default = null
}

variable "required_rdb_vpce" {
  description = "flag to note whether we should create a VPC Endpoint for RDB"
  default     = 0
}

variable "rdb_vpce_name" {
  type    = string
  default = null
}

variable "zone_id" {
  description = "route-53 zone domain name"
}

locals {
  tags = {
    SERVICEID   = "ts01695"
    MANAGED     = "terraform"
    PID         = "PID0462"
    TID         = "DPDA"
    ENVIRONMENT = var.environmentForTagging
  }
}
