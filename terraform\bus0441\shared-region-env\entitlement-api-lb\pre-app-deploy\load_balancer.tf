# Network Load Balancer For Entitlement Api
resource "aws_lb" "network_lb" {
  count                      = "${var.environment == "stg" ? 0 : 1}"
  name                       = "${var.environment == "prod" ? format("%s-nlb-prod", var.app_name) : format("%s-nlb-non-prod", var.app_name)}"
  internal                   = true
  load_balancer_type         = "network"
  enable_deletion_protection = false
  enable_cross_zone_load_balancing = true

  subnets = [
    "${module.region_data.private_subnet_1_id[0]}",
    "${module.region_data.private_subnet_2_id[0]}",
    "${module.region_data.private_subnet_3_id[0]}"
  ]

  security_groups = [
    module.region_data.security_group_private_app[0],
    module.region_data.security_group_private_web[0]
    ]

  idle_timeout = 300
  tags         = "${local.entitlement_api_tags}"
}

# Entitlement Api Network Load Balancer listener for dev
resource "aws_lb_listener" "network_lb_listener_dev" {
  count = "${var.region == "us-east-1" && var.environment == "dev" ? 1 : 0}"
  load_balancer_arn = "${aws_lb.network_lb[0].arn}"
  port              = 8089
  protocol          = "TLS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = "${data.aws_acm_certificate.entitlement_api_acm_certificate.arn}"

  default_action {
    target_group_arn = "${aws_lb_target_group.entitlement_api_network_target_group.arn}"
    type             = "forward"
  }
}

# Entitlement Api Network Load Balancer listener for stg
# Same network load balancer created in dev will be used by stg
resource "aws_lb_listener" "network_lb_listener_stg" {
  count =             var.stg_count
  load_balancer_arn = "${data.aws_lb.network_lb_stg[0].arn}"
  port              = 443
  protocol          = "TLS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = "${data.aws_acm_certificate.entitlement_api_acm_certificate.arn}"

  default_action {
    target_group_arn = "${aws_lb_target_group.entitlement_api_network_target_group_1.arn}"
    type             = "forward"
  }
  tags = merge(local.entitlement_api_tags, {
    NAME = "Blue"
  })
}

resource "aws_lb_listener" "network_lb_listener_stg_8443" {
  count =             var.stg_count
  load_balancer_arn = "${data.aws_lb.network_lb_stg[0].arn}"
  port              = 8443
  protocol          = "TLS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = "${data.aws_acm_certificate.entitlement_api_acm_certificate.arn}"

  default_action {
    target_group_arn = "${aws_lb_target_group.entitlement_api_network_target_group_2.arn}"
    type             = "forward"
  }
  tags = merge(local.entitlement_api_tags, {
    NAME = "Green"
  })
}

# Entitlement Api Network Load Balancer listener prod
resource "aws_lb_listener" "network_lb_listener_prod" {
  count =             var.prod_count
  load_balancer_arn = "${aws_lb.network_lb[0].arn}"
  port              = 443
  protocol          = "TLS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = "${data.aws_acm_certificate.entitlement_api_acm_certificate.arn}"

  default_action {
    target_group_arn = "${aws_lb_target_group.entitlement_api_network_target_group_1.arn}"
    type             = "forward"
  }
  tags = merge(local.entitlement_api_tags, {
    NAME = "Blue"
  })
}

resource "aws_lb_listener" "network_lb_listener_prod_8443" {
  count =             var.prod_count
  load_balancer_arn = "${aws_lb.network_lb[0].arn}"
  port              = 8443
  protocol          = "TLS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-2-2021-06"
  certificate_arn   = "${data.aws_acm_certificate.entitlement_api_acm_certificate.arn}"

  default_action {
    target_group_arn = "${aws_lb_target_group.entitlement_api_network_target_group_2.arn}"
    type             = "forward"
  }
  tags = merge(local.entitlement_api_tags, {
    NAME = "Green"
  })
}

# Entitlement Api Network Target Group
resource "aws_lb_target_group" "entitlement_api_network_target_group" {
  name     = "${var.app_name}-${var.environment}-ntg"
  port     = 443
  protocol = "TCP"
  vpc_id   = "${module.region_data.vpc_id[0]}"

  target_type = "ip"

  health_check {
    interval            = 90
    path                = "/velo/entitlement/health-check"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 60
    healthy_threshold   = 5
    unhealthy_threshold = 2
    matcher             = "200"
  }

  tags = "${local.entitlement_api_tags}"
}

resource "aws_lb_target_group" "entitlement_api_network_target_group_1" {
  name     = "${var.app_name}-${var.environment}-ntg-1"
  port     = 443
  protocol = "TCP"
  vpc_id   = "${module.region_data.vpc_id[0]}"

  target_type = "ip"

  health_check {
    interval            = 90
    path                = "/velo/entitlement/health-check"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 60
    healthy_threshold   = 5
    unhealthy_threshold = 2
    matcher             = "200"
  }

  tags = "${local.entitlement_api_tags}"
}

resource "aws_lb_target_group" "entitlement_api_network_target_group_2" {
  name     = "${var.app_name}-${var.environment}-ntg-2"
  port     = 443
  protocol = "TCP"
  vpc_id   = "${module.region_data.vpc_id[0]}"

  target_type = "ip"

  health_check {
    interval            = 90
    path                = "/velo/entitlement/health-check"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 60
    healthy_threshold   = 5
    unhealthy_threshold = 2
    matcher             = "200"
  }

  tags = "${local.entitlement_api_tags}"
}