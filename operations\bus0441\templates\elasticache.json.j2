{
    "ReplicationGroupId": "{{ target_cluster_name }}",
    "Engine": "Redis",
    "ReplicationGroupDescription": "{{ target_cluster_description }}",
    "NumNodeGroups": {{ nodenumber }},
    "CacheSubnetGroupName": "private-az-subnets",
    "EngineVersion": "{{ engine_version|default('7.0') }}",
    "CacheParameterGroupName": "{{ cache_parameter_group }}",
    "NodeGroupConfiguration": [
        {% for nodeConfig in node_group_configuration %}
            {{nodeConfig|tojson()}}
        {% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    "CacheNodeType": "{{ cache_node_type }}",
    "SecurityGroupIds": {{security_group_ids|tojson()}}
}