variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The name of the environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "app_name" {
  default = "entitlement-api"
}

variable "required" {
  default = 0
}

variable "stg_count" {
  default = 0
}

variable "prod_count" {
  default = 0
}

variable "kinesis_stream_name" {
}

variable "datasource_session_variable" {
  default = ""
}

variable "dp-ent-log_retention" {
  default = 7
}

locals {
  entitlement_api_tags = {
    SERVICEID   = "ts01894"
    ORG         = "MAAS"
    TID         = "DPDA"
    PID         = "PID0462"
    MANAGED     = "terraform"
    ENVIRONMENT = var.environmentForTagging
  }
}

