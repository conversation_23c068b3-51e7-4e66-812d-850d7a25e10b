region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "dev"
environmentForTagging = "dev"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

vpc                = "vpc-01453a03f6929cb2e"
mart_api_nlb_name  = "mart-api-nlb-stg"
environment_suffix = "dev"
account_env        = "nonprod"
account_id         = "************"
subscriber_aws_ids = ["arn:aws:iam::************:root","arn:aws:iam::************:root"]
required_replica   = 1