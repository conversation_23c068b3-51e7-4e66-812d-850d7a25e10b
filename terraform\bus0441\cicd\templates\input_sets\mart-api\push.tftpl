inputSet:
  name: ${name}
  identifier: ${identifier}
  tags:
    terraform_managed: ""
  orgIdentifier: ${org_id}
  projectIdentifier: ${project_id}
  pipeline:
    identifier: ${pipeline_id}
    variables:
      - name: BuildOption
        type: String
        value: force-build
      - name: DeployToEnvs
        type: String
        value: dev
      - name: TerraformRequireApproval
        type: String
        value: "no"
      - name: CM_Skip_Scanning
        type: String
        value: "${skip_checkmarx_scanning}"
    properties:
      ci:
        codebase:
          build:
            type: branch
            spec:
              branch: <+trigger.branch>