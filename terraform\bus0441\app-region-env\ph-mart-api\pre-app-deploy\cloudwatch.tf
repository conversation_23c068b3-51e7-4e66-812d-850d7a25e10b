resource "aws_cloudwatch_log_group" "ph_mart_api_fargate_log" {
  name              = "/ecs/ph-mart-api-fargate-${var.environment}"
  retention_in_days = "7"
  tags              = local.tags
}

resource "aws_cloudwatch_log_subscription_filter" "ph_mart_api_fargate_log_subscription" {
  name            = "ph-mart-api-fargate-log-subscription-${var.environment}"
  log_group_name  = aws_cloudwatch_log_group.ph_mart_api_fargate_log.name
  filter_pattern  = ""
  destination_arn = data.aws_kinesis_stream.stream.arn
  role_arn        = data.aws_iam_role.log_role.arn
}