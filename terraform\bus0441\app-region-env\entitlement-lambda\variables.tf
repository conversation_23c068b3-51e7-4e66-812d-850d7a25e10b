variable "region" {
  description = "Region"
  default     = "us-east-1"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

variable "release_version" {
  type = string
  default = ""
}

variable "dr_region" {
  default     = "us-west-2"
  description = "The region of the DR resources"
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "account_env" {
}


variable "required" {
  default = 0
}

variable "image_tag" {
  default     = "latest"
  description = "The docker image tag passed by jenkins"
}

variable "datasource_session_variable" {
  default = ""
}

variable "uim_email_arn" {}

variable "port" {
  default = "2773"
}

variable "extension_arn" {
  description = "Extension ARNs for the x86_64"
}

variable "release_management_lambda_count" {
  default     = 0
  description = "control deployment of lambda to specific regions and environments"
}

locals {
  tags = {
    MANAGED     = "terraform"
    ENVIRONMENT = var.environmentForTagging,
    PID         = "PID0462"
    SERVICEID   = "ts01894"
    TID         = "DPDA"
    FUNCTION    = "app"
  }
  lambda_s3_bucket = "entitlement-deploy-${var.account_env}-${var.region}"
}