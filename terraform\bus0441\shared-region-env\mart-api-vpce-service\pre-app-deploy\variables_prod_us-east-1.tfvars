region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

mart_api_nlb_arn         = "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/net/mart-api-nlb-prod/03c7b2e602357078"
has_vpce_service         = true
vpce_service_private_dns = "investmentapi-prod.dpd6b927.eas.morningstar.com"
vpce_service_allowed_principals = [
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::062740692535:root",
  "arn:aws:iam::897791612364:root",
  "arn:aws:iam::230310287888:root",
  "arn:aws:iam::************:root"
]
vpce_subnet_ids = [
  "subnet-0c3db8d6d567a4f4a", # us-east-1a-private
  "subnet-0d610a03d59c8889d"  #	us-east-1b-private
]
