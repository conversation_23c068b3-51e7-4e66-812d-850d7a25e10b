resource "aws_lambda_function" "config_update_function" {
  function_name    = "mart-datapoint-config-update-${var.environment}"
  description      = "A lambda function to trigger mart datapoint config update"
  role             = data.aws_iam_role.mart-role.arn
  runtime          = "python3.11"
  handler          = "function.handler"
  s3_bucket        = "dpda-deploy-${var.account_env}-${var.region}"
  s3_key           = "mart-api-lambda/${var.release_version}/function.zip"
  source_code_hash = base64sha256("mart-api-lambda/${var.release_version}/function.zip")
  timeout          = 600
  memory_size      = 128
  reserved_concurrent_executions = "3"

  vpc_config {
    subnet_ids = [
      module.region_data.private_subnet_1_id[0],
      module.region_data.private_subnet_2_id[0],
      module.region_data.private_subnet_3_id[0]
    ]

    security_group_ids = [
      module.region_data.security_group_private_app[0],
      module.region_data.security_group_private_web[0]
    ]
  }

  tags = local.tags

  environment {
    variables = {
      mart_url = var.config_trigger_url
    }
  }
}

resource "aws_s3_bucket_notification" "config-update-trigger" {
    bucket                  = var.bucket_for_config_update
    lambda_function {
        lambda_function_arn = aws_lambda_function.config_update_function.arn
        events              = ["s3:ObjectCreated:Put"]
        filter_prefix       = "green-env-config/view/datapoints_view"

    }
    lambda_function {
      lambda_function_arn = aws_lambda_function.config_update_function.arn
      events              = ["s3:ObjectCreated:Put"]
      filter_prefix       = "blue-env-config/view/datapoints_view"
    }
}

resource "aws_lambda_permission" "allow_s3_cross_account" {
  statement_id  = "AllowExecutionFromS3CrossAccount"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.config_update_function.function_name
  principal     = "s3.amazonaws.com"
  source_arn    = "arn:aws:s3:::${var.bucket_for_config_update}"
  source_account = var.dataac_account_id
}

resource "aws_lambda_function" "rdb_function" {
  function_name                  = "clean-mart-rdb-cache-${var.environment}"
  description                    = "tool to update rdb cache"
  s3_bucket                      = "dpda-deploy-${var.account_env}-${var.region}"
  s3_key                         = "mart-api-lambda/${var.release_version}/app.jar"
  role                           = data.aws_iam_role.mart-role.arn
  runtime                        = "java11"
  handler                        = "com.morningstar.cachemanager.handler.RDBCacheHandler::handleRequest"
  timeout                        = "600"
  layers                         = [var.extension_arn]
  memory_size                    = "1024"

  vpc_config {
    subnet_ids = [
      module.region_data.private_subnet_1_id[0],
      module.region_data.private_subnet_2_id[0],
      module.region_data.private_subnet_3_id[0]
    ]

    security_group_ids = [
      module.region_data.security_group_private_app[0],
      module.region_data.security_group_private_db[0],
      module.region_data.security_group_private_web[0]
    ]
  }

  tags = local.tags

  environment {
    variables = {
      mart_profiles_active = var.environment
      parameters_secrets_extension_http_port  = var.port
    }
  }
}

resource "aws_lambda_event_source_mapping" "rdb_sqs_subscribe" {
  event_source_arn = "${aws_sqs_queue.rdb_sqs.arn}"
  function_name    = "${aws_lambda_function.rdb_function.arn}"
}


