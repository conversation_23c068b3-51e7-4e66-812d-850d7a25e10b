resource "aws_cloudwatch_log_group" "entitlement-api" {
  count = "${var.required}"
  name = "/ecs/entitlement-api-${var.environment}"
  retention_in_days = "${var.dp-ent-log_retention}"
  tags = "${local.entitlement_api_tags}"
}

resource "aws_cloudwatch_log_subscription_filter" "velo_entitlement_log-subscription-filter" {
  count           = "${var.required}"
  name            = "SplunkFilter"
  log_group_name  = "${aws_cloudwatch_log_group.entitlement-api[0].name}"
  filter_pattern  = ""
  destination_arn = "${data.aws_kinesis_stream.stream[0].arn}"
  role_arn        = "${data.aws_iam_role.log_role.arn}"
}
