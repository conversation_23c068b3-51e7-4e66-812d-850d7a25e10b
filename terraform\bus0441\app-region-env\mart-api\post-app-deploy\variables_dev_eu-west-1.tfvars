region                = "eu-west-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "dev"
environmentForTagging = "dev"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

task_desired_count = "1"
peak_desired_count = "1"
zone_id            = "dpd8eb6b.easn.morningstar.com"
mart_api_nlb_name  = "mart-api-nlb-stg"
mart_api_alb_name  = "mart-api-alb-dev"
tcp80_port         = 8080
tcp443_port        = 443
domain_name        = "*.dpd8eb6b.easn.morningstar.com"
