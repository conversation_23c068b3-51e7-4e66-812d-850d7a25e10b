resource "aws_sns_topic" "clear_cache_message_topic" {
  name = "clear-cache-topic-${var.environment}"
  tags = local.tags
}

resource "aws_sns_topic" "rdb_sqs_alarm_email_topic" {
  name = "rdb-sqs-alarm-email-topic-${var.environment}"
  tags = local.tags
}

resource "aws_sns_topic_subscription" "rdb_sqs_alarm_email_topic_subscription" {
  topic_arn = aws_sns_topic.rdb_sqs_alarm_email_topic.arn
  count = var.rdb_sqs_alarm_monitor_count
  protocol  = "email"
  endpoint  = "<EMAIL>"
}