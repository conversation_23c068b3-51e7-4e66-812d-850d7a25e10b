region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "dev"
environmentForTagging = "dev"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

required                 = 1
account_id               = "************"
velo_role                = "arn:aws:iam::************:role/velo-stand-alone-dev"
redshift_assume_role     = "arn:aws:iam::************:role/cz_r_************_mart-role-dev"
asyncdata_feed_worker    = "arn:aws:iam::************:role/asyncdata/stg-ecs-asyncdata-feed-worker"
asyncdata_feed_worker_dr = "arn:aws:iam::************:role/asyncdata/stg-dr-ecs-asyncdata-feed-worker"
velo_test_role           = "arn:aws:iam::************:role/velo-ecs-role-qatest-dev"
dataac_lambda_role       = "arn:aws:iam::************:role/lambda-execution-role"


