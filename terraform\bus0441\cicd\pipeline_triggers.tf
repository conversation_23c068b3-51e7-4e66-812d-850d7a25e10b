module "async-mart-api" {
  source      = "./../../modules/pipeline_triggers"
  org_id      = var.org_id
  project_id  = var.project_id
  pipeline_id = "asyncmartapi"
  repo_name   = "dataac/async-mart-api"
  custom_input_sets = [
    {
      identifier = "pr_to_input_set"
      name       = "PR-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/pr.tftpl"
        variables = {
          skip_blackduck_scanning  = "yes"
          skip_checkmarx_scanning = "no"
        }
      }
    },
    {
      identifier = "push_to_input_set"
      name       = "Push-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/push.tftpl"
        variables = {
          skip_blackduck_scanning  = "no"
          skip_checkmarx_scanning = "no"
        }
      }
    }
  ]
  triggers = {
    pr_to_develop   = true
    pr_to_release   = true
    pr_to_master    = false
    push_to_master  = false
    push_to_develop = true
  }
}

# Push to Dev needs adjustments
module "entitlement-api" {
  source      = "./../../modules/pipeline_triggers"
  org_id      = var.org_id
  project_id  = var.project_id
  pipeline_id = "entitlementapi"
  repo_name   = "dataac/velo-entitlement"
  custom_input_sets = [
    {
      identifier = "pr_to_input_set"
      name       = "PR-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/pr.tftpl"
        variables = {
          skip_checkmarx_scanning = "yes"
        }
      }
    },
    {
      identifier = "push_to_input_set"
      name       = "Push-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/push.tftpl"
        variables = {
          skip_checkmarx_scanning = "yes"
        }
      }
    },
    {
      identifier = "push_to_develop_input_set"
      name       = "Push-to-develop-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/entitlement-api/push.tftpl"
        variables = {
          skip_checkmarx_scanning = "yes"
        }
      }
    }
  ]
  custom_triggers = [
    {
      identifier = "push_to_develop"
      name       = "Push-to-develop"
      template = {
        file_path = "${path.module}/templates/triggers/default/push_branch_trigger.tftpl"
        variables = {
          operator     = "Equals"
          branch       = "develop"
          input_set_id = "push_to_develop_input_set"
        }
      }
    }
  ]
  triggers = {
    pr_to_develop   = true
    pr_to_release   = true
    pr_to_master    = true
    push_to_master  = true
    push_to_develop = false # Disabled to use a custom trigger that deploys to dev
  }
}


module "mart-api" {
  source      = "./../../modules/pipeline_triggers"
  org_id      = var.org_id
  project_id  = var.project_id
  pipeline_id = "martapi"
  repo_name   = "dataac/mart-api"
  custom_input_sets = [
    {
      identifier = "pr_to_input_set"
      name       = "PR-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/pr.tftpl"
        variables = {
          skip_blackduck_scanning = "yes"
          skip_checkmarx_scanning = "no"
        }
      }
    },
    {
      identifier = "push_to_input_set"
      name       = "Push-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/push.tftpl"
        variables = {
          skip_blackduck_scanning = "yes"
          skip_checkmarx_scanning = "no"
        }
      }
    },
    {
      identifier = "push_to_develop_input_set"
      name       = "Push-to-develop-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/mart-api/push.tftpl"
        variables = {
          skip_checkmarx_scanning = "no"
          skip_blackduck_scanning = "no"
        }
      }
    }
  ]
  custom_triggers = [
    {
      identifier = "push_to_develop"
      name       = "Push-to-develop"
      template = {
        file_path = "${path.module}/templates/triggers/default/push_branch_trigger.tftpl"
        variables = {
          operator     = "Equals"
          branch       = "develop"
          input_set_id = "push_to_develop_input_set"
        }
      }
    }
  ]
  triggers = {
    pr_to_develop   = true
    pr_to_release   = true
    pr_to_master    = true
    push_to_master  = true
    push_to_develop = false # Disabled to use a custom trigger that deploys to dev
  }
}

module "mart-api-lambda" {
  source      = "./../../modules/pipeline_triggers"
  org_id      = var.org_id
  project_id  = var.project_id
  pipeline_id = "martapilambda"
  repo_name   = "dataac/mart-api-lambda"
  custom_input_sets = [
    {
      identifier = "pr_to_input_set"
      name       = "PR-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/pr.tftpl"
        variables = {
          skip_blackduck_scanning  = "yes"
          skip_checkmarx_scanning = "no"
        }
      }
    },
    {
      identifier = "push_to_input_set"
      name       = "Push-to-input-set"
      template = {
        file_path = "${path.module}/templates/input_sets/default/push.tftpl"
        variables = {
          skip_blackduck_scanning  = "no"
          skip_checkmarx_scanning = "no"
        }
      }
    }
  ]
  triggers = {
    pr_to_develop   = true
    pr_to_release   = true
    pr_to_master    = true
    push_to_master  = true
    push_to_develop = true
  }
}