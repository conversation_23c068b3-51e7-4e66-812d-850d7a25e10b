variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "mart_api_nlb_arn" {
  description = "mart api nlb ARN"
  default     = null
}

variable "has_vpce_service" {
  description = "flag to note whether we should create a VPC Endpoint Service for mart-api in this env"
  default     = false
}

variable "vpce_service_private_dns" {
  description = "private dns value to assign to vpce service"
  default     = null
}

variable "vpce_service_allowed_principals" {
  default = null
}

variable "vpce_subnet_ids" {
  type    = list(string)
  default = []
}

locals {
  tags = {
    SERVICEID   = "ts01695"
    MANAGED     = "terraform"
    PID         = "PID0462"
    TID         = "DPDA"
    ENVIRONMENT = var.environmentForTagging
  }
}
