resource "aws_vpc_endpoint_service" "mart_api_endpoint_service" {
  count                      = var.has_vpce_service ? 1 : 0
  acceptance_required        = false
  network_load_balancer_arns = [var.mart_api_nlb_arn]
  private_dns_name           = var.vpce_service_private_dns
  allowed_principals         = var.vpce_service_allowed_principals
  supported_ip_address_types = ["ipv4"]
  tags = merge(tomap({
    Name = "mart-api-endpoint-service-${var.environmentForTagging}"
    }),
  local.tags)
}
