launchType: FARGATE
serviceName: stg-entitlement-api-service-nlb
desiredCount: 0
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0a862c74582ddf040
      - sg-0220dde95ed0e2884
      - sg-0df8f5f2425904528
    subnets:
      - subnet-05833c296327b5241
      - subnet-0c7ad2143f5e9a8dd
      - subnet-02eb4a4d13d029c56
    assignPublicIp: DISABLED

deploymentConfiguration:
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 300

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: stg-entitlement-api
    containerPort: 8443

tags:
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: ORG
    value: <+serviceVariables.ORG>
  - key: TID
    value: <+variable.TID>
  - key: PID
    value: <+serviceVariables.PID>
  - key: MANAGED
    value: <+serviceVariables.MANAGED>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>

propagateTags: TASK_DEFINITION