data "aws_iam_role" "mart-role" {
  name = "mart-role-${var.environment}"
}

data "aws_ecs_cluster" "mart-cluster" {
  cluster_name = "mart-service-cluster-${var.environment}"

  //reference cluster_name and arn
}

data "aws_ecs_service" "ph_mart_api_fargate_nlb_service" {
  service_name = "ph-mart-api-fargate-nlb-${var.environment}"
  cluster_arn  = data.aws_ecs_cluster.mart-cluster.arn
}

data "aws_lb" "mart_api_nlb" {
  name = var.mart_api_nlb_name
}

data "aws_route53_zone" "zone" {
  name = "${var.zone_id}."
}