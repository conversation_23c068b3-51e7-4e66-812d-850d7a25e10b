resource "aws_route53_record" "customcalcapi" {
  count   = var.has_custom_calc_vpce
  zone_id = data.aws_route53_zone.zone.id
  name    = "customcalcapi-${var.environment}.${data.aws_route53_zone.zone.name}"
  type    = "A"
  alias {
    name                   = aws_vpc_endpoint.custom_calc_vpce[count.index].dns_entry[0].dns_name
    zone_id                = aws_vpc_endpoint.custom_calc_vpce[count.index].dns_entry[0].hosted_zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "rdb" {
  count   = var.required_rdb_vpce
  zone_id = data.aws_route53_zone.zone.id
  name    = "rdb-${var.region}.${data.aws_route53_zone.zone.name}"
  type    = "CNAME"
  ttl     = "300"

  records = [aws_vpc_endpoint.rdb_vpce[0].dns_entry[0].dns_name]
}