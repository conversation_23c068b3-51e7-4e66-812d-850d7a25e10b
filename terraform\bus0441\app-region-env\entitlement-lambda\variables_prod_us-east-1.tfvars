region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

account_env                = "prod"
required                   = 1
extension_arn              = "arn:aws:lambda:us-east-1:************:layer:AWS-Parameters-and-Secrets-Lambda-Extension:11"
uim_email_arn              = "arn:aws:sns:us-east-1:************:uim-user-awsprod"
