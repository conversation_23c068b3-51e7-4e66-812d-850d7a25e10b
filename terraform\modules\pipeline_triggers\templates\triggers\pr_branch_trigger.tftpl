trigger:
  name: ${trigger_name}
  identifier: ${trigger_id}
  enabled: true
  description: ""
  tags: {
    terraform_managed: ""
  }
  orgIdentifier: ${org_id}
  projectIdentifier: ${project_id}
  pipelineIdentifier: ${pipeline_id}
  stagesToExecute: []
  source:
    type: Webhook
    spec:
      type: Bitbucket
      spec:
        type: PullRequest
        spec:
          connectorRef: account.msstash
          autoAbortPreviousExecutions: true
          payloadConditions:
            - key: targetBranch
              operator: ${operator}
              value: ${branch}
          headerConditions: []
          repoName: ${repo_name}
          actions:
            - Create
            - Update
  inputSetRefs:
    - ${input_set_id}