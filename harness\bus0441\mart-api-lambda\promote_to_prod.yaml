name: Copy Artifacts from non-prod to prod account
items:
  - source:
      accountId: "************"
      region: us-east-1
      bucket: dpda-deploy-nonprod-us-east-1
      key: mart-api-lambda/{{sourceVersion}}/app.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: dpda-deploy-prod-us-east-1
        key: mart-api-lambda/{{targetVersion}}/app.jar
      - accountId: "************"
        region: eu-west-1
        bucket: dpda-deploy-prod-eu-west-1
        key: mart-api-lambda/{{targetVersion}}/app.jar
  - source:
      accountId: "************"
      region: us-east-1
      bucket: dpda-deploy-nonprod-us-east-1
      key: mart-api-lambda/{{sourceVersion}}/function.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: dpda-deploy-prod-us-east-1
        key: mart-api-lambda/{{targetVersion}}/function.zip
      - accountId: "************"
        region: eu-west-1
        bucket: dpda-deploy-prod-eu-west-1
        key: mart-api-lambda/{{targetVersion}}/function.zip
