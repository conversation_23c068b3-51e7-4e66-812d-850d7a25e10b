region                = "eu-west-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

mart_api_nlb_arn = "arn:aws:elasticloadbalancing:eu-west-1:************:loadbalancer/net/mart-api-nlb-prod/b74a4482e7cf78ca"
has_vpce_service = true
vpce_service_allowed_principals = [
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root"
]
vpce_subnet_ids = [
  "subnet-09ccfc3620f652857", # eu-west-1a-private
  "subnet-028c31b8c943d1a56"  # eu-west-1b-private
]
