region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "qa"
environmentForTagging = "qa"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

task_desired_count        = "1"
peak_desired_count        = "2"
zone_id                   = "dpd8eb6b.easn.morningstar.com"
mart_api_nlb_name         = "mart-api-nlb-stg"
mart_api_alb_name         = "mart-api-alb-qa"
tcp80_port                = 8080
tcp443_port               = 443
primary_route_53_required = true
domain_name               = "*.dpd8eb6b.easn.morningstar.com"