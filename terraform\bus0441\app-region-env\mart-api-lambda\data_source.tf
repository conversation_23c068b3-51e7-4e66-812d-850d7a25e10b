module "region_data" {
  source = "git::ssh://msstash.morningstar.com/cloudsvc/tf-module-infrastructure-data.git?ref=feature/terraform1.3"
}


data "aws_iam_role" "mart-role" {
  name = "mart-role-${var.environment}"
}

data "aws_caller_identity" "current" {}

data "aws_kinesis_stream" "stream" {
  name = "mstar-kinesis-dpda-${var.environment == "prod" ? "prod" : "non-prod"}-splunk-${var.region}"
}
data "aws_iam_role" "log_role" {
  name = "CWLtoKinesis"
}