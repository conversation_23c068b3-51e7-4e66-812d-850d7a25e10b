family: async-api-service-task-definition-qa
executionRoleArn: arn:aws:iam::<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:role/mart-role-qa
taskRoleArn: arn:aws:iam::<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:role/mart-role-qa
containerDefinitions:
  - name: async-mart-api-fargate-container-qa
    image: <+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>.dkr.ecr.<+infra.region>.amazonaws.com/async-mart-api-ecr:<+serviceVariables.ImageTag>
    cpu: 2048
    portMappings:
      - hostPort: 8080
        protocol: tcp
        containerPort: 8080
    essential: true
    environment:
      - name: ACTIVE_ENV
        value: qa
      - name: AWS_SQS_QUEUE
        value: async-mart-api-message-qa
    logConfiguration:
      logDriver: awslogs
      options:
        awslogs-group: /ecs/async-mart-api-fargate-qa
        awslogs-region: us-east-1
        awslogs-stream-prefix: ecs
    secrets:
      - name: MARTGATEWAY_RDB_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:parameter/MART/INVESTAPI_READER_UAT1"
      - name: MARTGATEWAY_REDSHIFT_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:parameter/LAKEHOUSE/ADMIN_PASSWORD"
      - name: MONGODB_URI
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:parameter/MART/MONGO_DB_CONN_STRING"
      - name: MARTGATEWAY_UIM_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:parameter/MART/ENTITLEMENT_ADMIN_PASSWORD"
      - name: MARTGATEWAY_EOD_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:parameter/MART/EOD/INVESTMENT_SERVICE_READER"
      - name: MARTGATEWAY_RDS_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:parameter/MART/FIXEDINCOME_DB_PASSWORD"
      - name: MARTGATEWAY_CUSTOM-DATA_PASSWORD
        valueFrom: "arn:aws:ssm:<+infra.region>:<+variable.AWS_DPDA_NONPROD_ACCOUNT_ID>:parameter/MART/CUSTOM_DATA/INVAPI_READ_ONLY"
cpu: '2048'
memory: '8192'
requiresCompatibilities:
  - FARGATE
networkMode: awsvpc
runtimePlatform:
  cpuArchitecture: X86_64
  operatingSystemFamily: LINUX
tags:
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>