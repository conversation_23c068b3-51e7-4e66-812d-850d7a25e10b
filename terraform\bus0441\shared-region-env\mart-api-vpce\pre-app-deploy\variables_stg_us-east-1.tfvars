region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "stg"
environmentForTagging = "stg"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

zone_id                       = "dpd8eb6b.easn.morningstar.com"
has_custom_calc_vpce          = 1
custom_calc_vpce_service_name = "com.amazonaws.vpce.us-east-1.vpce-svc-0069e4ad97f45662c"
has_mart_api_vpce_service     = 1
mart_api_vpce_service_name    = "com.amazonaws.vpce.us-east-1.vpce-svc-021f664637ee9edd0"
required_rdb_vpce             = 1
rdb_vpce_name                 = "RDB-AWS-UAT"
rdb_vpce_service_name         = "com.amazonaws.vpce.us-east-1.vpce-svc-0ad8261264b437374"
vpce_subnet_ids = [
  "subnet-038ca9497049287b6", # us-east-1a-private
  "subnet-02a941119c6855102"  #	us-east-1b-private
]
