region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "qa"
environmentForTagging = "qa"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

required                 = 1
account_id               = "************"
velo_role                = "arn:aws:iam::************:role/velo-stand-alone-stg"
redshift_assume_role     = "arn:aws:iam::************:role/cz_r_************_mart-role-stg"
asyncdata_feed_worker    = "arn:aws:iam::************:role/asyncdata/stg-ecs-asyncdata-feed-worker"
asyncdata_feed_worker_dr = "arn:aws:iam::************:role/asyncdata/stg-dr-ecs-asyncdata-feed-worker"
velo_test_role           = "arn:aws:iam::************:role/velo-ecs-role-qatest-stg"
dataac_lambda_role       = "arn:aws:iam::************:role/lambda-execution-role"