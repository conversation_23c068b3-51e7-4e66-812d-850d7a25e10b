variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "task_desired_count" {
  description = "expected instance number"
}

variable "peak_desired_count" {
  description = "expected instance number"
}

variable "ecs_nlb_cpu_low_threshold" {
  default = "20"
}

variable "ecs_nlb_cpu_high_threshold" {
  default = "80"
}

variable "zone_id" {
  description = "route-53 zone domain name"
}

variable "mart_api_nlb_name" {
  description = "load balancer used by mart-api"
}

variable "required" {
  default = 0
}

variable "weight" {
}

variable "domain_name" {
}

locals {
  tags = {
    SERVICEID   = "ts01695"
    MANAGED     = "terraform"
    PID         = "PID0462"
    TID         = "DPDA"
    ENVIRONMENT = var.environmentForTagging
  }
}
