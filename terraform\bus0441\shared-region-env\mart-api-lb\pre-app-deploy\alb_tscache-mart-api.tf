# ################################################################################
# # START OF TEMP SECTION
# # TODO Remove after Production release
# resource "aws_lb_target_group" "tscache_mart_api_nlb_fargate_target_group" {
#   name        = "tscache-mart-api-tg-nlb-${var.environment}"
#   target_type = "ip"
#   port        = 8080
#   protocol    = "HTTP"
#   vpc_id      = var.vpc
#
#   health_check {
#     healthy_threshold   = "3"
#     interval            = "30"
#     protocol            = "HTTP"
#     matcher             = "200-399"
#     timeout             = "3"
#     path                = "/investment-api/v1/health-check"
#     unhealthy_threshold = "3"
#   }
#   tags = local.tags
# }
#
# # TODO Remove after production release
# resource "aws_lb_listener_rule" "tsapi_url_based_routing1" {
#   count        = var.use_tscache_listener_rule ? 1 : 0
#   listener_arn = aws_lb_listener.https443.arn
#   priority     = 9
#   action {
#     type             = "forward"
#     target_group_arn = aws_lb_target_group.tscache_mart_api_nlb_fargate_target_group.arn
#   }
#   condition {
#     path_pattern {
#       values = [
#         "/investment-api/v1/time-series-data/*",
#         "/v1/time-series-data/investments/*"
#       ]
#     }
#   }
#   tags = local.tags
# }
#
# # TODO Remove after production release
# moved {
#   from = aws_lb_listener_rule.tsapi_url_based_routing1
#   to   = aws_lb_listener_rule.tsapi_url_based_routing1[0]
# }
# # END OF TEMP SECTION
# ################################################################################

//listener 443 with https protocol
//will use this target group.  Otherwise tcp will conflict with https.
resource "aws_lb_target_group" "tscache_mart_api_nlb_fargate_target_group_1" {
  name                          = "tscache-mart-api-tg-nlb-${var.environment}-1"
  target_type                   = "ip"
  port                          = 8080
  protocol                      = "HTTP"
  vpc_id                        = var.vpc
  load_balancing_algorithm_type = "least_outstanding_requests"

  health_check {
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTP"
    matcher             = "200-399"
    timeout             = "3"
    path                = "/investment-api/v1/health-check"
    unhealthy_threshold = "3"
  }
  tags = local.tags
}

resource "aws_lb_target_group" "tscache_mart_api_nlb_fargate_target_group_2" {
  name                          = "tscache-mart-api-tg-nlb-${var.environment}-2"
  target_type                   = "ip"
  port                          = 8080
  protocol                      = "HTTP"
  vpc_id                        = var.vpc
  load_balancing_algorithm_type = "least_outstanding_requests"

  health_check {
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTP"
    matcher             = "200-399"
    timeout             = "3"
    path                = "/investment-api/v1/health-check"
    unhealthy_threshold = "3"
  }
  tags = local.tags
}


// url based routing now listens to both host header and path pattern
resource "aws_lb_listener_rule" "tsapi_url_based_routing_blue" {
  count        = var.use_tscache_listener_rule ? 1 : 0
  listener_arn = aws_lb_listener.https443.arn
  priority     = 90
  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.tscache_mart_api_nlb_fargate_target_group_1.arn
  }
  condition {
    path_pattern {
      values = [
        "/investment-api/v1/time-series-data/*",
        "/v1/time-series-data/investments/*"
      ]
    }
  }

  lifecycle {
    #noinspection HILUnresolvedReference
    ignore_changes = [
      # Ignoring because harness will change the value during a B/G swap
      action[0].target_group_arn
    ]
  }

  tags = merge(local.tags, {
    NAME = "TS Blue"
  })
}

resource "aws_lb_listener_rule" "tsapi_url_based_routing_green" {
  count        = var.use_bg_deployment && var.use_tscache_listener_rule ? 1 : 0
  listener_arn = aws_lb_listener.https8443[0].arn
  priority     = 91
  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.tscache_mart_api_nlb_fargate_target_group_2.arn
  }
  condition {
    path_pattern {
      values = [
        "/investment-api/v1/time-series-data/*",
        "/v1/time-series-data/investments/*"
      ]
    }
  }

  lifecycle {
    #noinspection HILUnresolvedReference
    ignore_changes = [
      # Ignoring because harness will change the value during a B/G swap
      action[0].target_group_arn
    ]
  }

  tags = merge(local.tags, {
    NAME = "TS Green"
  })
}

# Added count to listener rule so need to move the resource internally to an array
moved {
  from = aws_lb_listener_rule.tsapi_url_based_routing_blue
  to   = aws_lb_listener_rule.tsapi_url_based_routing_blue[0]
}
