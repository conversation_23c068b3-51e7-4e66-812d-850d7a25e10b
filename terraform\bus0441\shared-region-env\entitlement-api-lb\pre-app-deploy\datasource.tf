module "region_data" {
  source = "git::ssh://msstash.morningstar.com/cloudsvc/tf-module-infrastructure-data.git?ref=feature/terraform1.3"
}

data "aws_region" "current" {}

data "aws_iam_role" "log_role" {
  name = "CWLtoKinesis"
}

data "aws_kinesis_stream" "stream" {
  count= var.required
  name = var.kinesis_stream_name
}

data "aws_lb" "network_lb_stg"{
  count = var.stg_count
  name = "${format("%s-nlb-non-prod", var.app_name)}"
}

data "aws_acm_certificate" "entitlement_api_acm_certificate" {
  domain      = "${var.environment == "prod" ? "*.dat688b3.eas.morningstar.com" : "*.date7ebe.easn.morningstar.com"}"
  types       = ["AMAZON_ISSUED"]
  most_recent = true
}