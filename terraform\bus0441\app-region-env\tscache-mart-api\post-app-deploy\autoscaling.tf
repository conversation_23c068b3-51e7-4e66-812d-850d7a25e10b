#########################################################################################
## Resources now being handled using Cloud formation templates located in the aws folder
#########################################################################################
#
# resource "aws_appautoscaling_target" "tscache_mart_api_fargate_nlb_scale_target" {
#   max_capacity       = 16
#   min_capacity       = var.task_desired_count
#   resource_id        = "service/${data.aws_ecs_cluster.mart-cluster.cluster_name}/${data.aws_ecs_service.tscache_mart_api_fargate_nlb_service.service_name}"
#   role_arn           = data.aws_iam_role.mart-role.arn
#   scalable_dimension = "ecs:service:DesiredCount"
#   service_namespace  = "ecs"
# }
#
# resource "aws_appautoscaling_scheduled_action" "tscache_mart_api_fargate_nlb_peak_schedule_up" {
#   name               = "tscache-mart-api-fargate-peak-schedule-up-${var.environment}"
#   service_namespace  = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.service_namespace
#   resource_id        = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.resource_id
#   scalable_dimension = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.scalable_dimension
#   schedule           = "cron(50 5,23 * * ? *)"
#
#   scalable_target_action {
#     min_capacity = var.peak_desired_count
#     max_capacity = 16
#   }
# }
#
# resource "aws_appautoscaling_scheduled_action" "tscache_mart_api_fargate_nlb_peak_schedule_down" {
#   name               = "tscache-mart-api-fargate-peak-schedule-down-${var.environment}"
#   service_namespace  = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.service_namespace
#   resource_id        = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.resource_id
#   scalable_dimension = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.scalable_dimension
#   schedule           = "cron(5 4,10 * * ? *)"
#
#   scalable_target_action {
#     min_capacity = var.task_desired_count
#     max_capacity = 16
#   }
# }
#
# resource "aws_appautoscaling_policy" "tscache_mart_api_fargate_nlb_up" {
#   name               = "tscache-mart-api-fargate-scale-up-${var.environment}"
#   service_namespace  = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.service_namespace
#   resource_id        = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.resource_id
#   scalable_dimension = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.scalable_dimension
#
#   step_scaling_policy_configuration {
#     adjustment_type         = "ChangeInCapacity"
#     cooldown                = 120
#     metric_aggregation_type = "Average"
#
#     step_adjustment {
#       metric_interval_lower_bound = 0
#       scaling_adjustment          = 1
#     }
#   }
# }
#
# resource "aws_appautoscaling_policy" "tscache_mart_api_fargate_nlb_down" {
#   name               = "tscache-mart-api-fargate-scale-down-${var.environment}"
#   service_namespace  = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.service_namespace
#   resource_id        = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.resource_id
#   scalable_dimension = aws_appautoscaling_target.tscache_mart_api_fargate_nlb_scale_target.scalable_dimension
#
#   step_scaling_policy_configuration {
#     adjustment_type         = "ChangeInCapacity"
#     cooldown                = 300
#     metric_aggregation_type = "Average"
#
#     step_adjustment {
#       metric_interval_upper_bound = 0
#       scaling_adjustment          = -1
#     }
#   }
# }