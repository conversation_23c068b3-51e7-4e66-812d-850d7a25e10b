launchType: FARGATE
serviceName:
desiredCount: 0
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-01116f50a47ead028  private_web
      - sg-0c62d1668d1a136cd  private_app
      - sg-04369231c2b3296ce  private_db
      - sg-0990b9c75cae7eebd  private_internal_email_morningstar
    subnets:
      - subnet-001ccb29fa360fac8
      - subnet-04b0eee56af730a20
      - subnet-00a0c2588e6af526c
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50

#loadBalancers:
#  - targetGroupArn: ## TODO Fill in targetGroupArn after creating in prod/us-west-2
#    containerName: mart-api-fargate-container-prod
#    containerPort: 8080