region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "qa"
environmentForTagging = "qa"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

config_trigger_url       = "http://martapi-qa.dpd8eb6b.easn.morningstar.com/v1/data-point/publish"
required                 = 1
rdb_topic_sqs            = "arn:aws:sns:us-east-1:************:dbops-rdb-flattablestatus"
extension_arn            = "arn:aws:lambda:us-east-1:************:layer:AWS-Parameters-and-Secrets-Lambda-Extension:11"
bucket_for_config_update = "mart-data-qa"
dataac_account_id        = "************"