variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

variable "dr_region" {
  default     = "us-west-2"
  description = "The region of the DR resources"
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################



variable "vpc_name" {
  description = "The name of the VPC"
}

locals {
  is_dr = var.region == var.dr_region
  tags = {
    MANAGED     = "terraform"
    TID         = "DPDA"
    ENVIRONMENT = var.environmentForTagging
  }
}
