module "dpda-code-deploy" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "3.15.2"
  count   = local.is_dr ? 0 : 1

  bucket = "dpda-deploy-${var.environment}-${var.region}"
  acl    = "private"

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  lifecycle_rule = [
    {
      id      = "expiration"
      enabled = true
      filter  = {} # All Objects

      expiration = {
        days = 90
      }
    }
  ]
  tags = merge(local.tags,
    {
      SERVICEID = "ts01894"
  })
}

module "dpda-code-deploy-dr" {
  source  = "terraform-aws-modules/s3-bucket/aws"
  version = "3.15.2"
  count   = local.is_dr ? 0 : 1

  providers = {
    aws = aws.dr
  }

  bucket = "dpda-deploy-${var.environment}-${var.dr_region}"
  acl    = "private"

  control_object_ownership = true
  object_ownership         = "ObjectWriter"

  lifecycle_rule = [
    {
      id      = "expiration"
      enabled = true
      filter  = {} # All Objects

      expiration = {
        days = 90
      }
    }
  ]
  tags = merge(local.tags,
    {
      SERVICEID = "ts01894"
  })
}