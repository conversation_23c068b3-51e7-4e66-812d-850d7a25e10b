deploy_role_name: mstar-engr-cross-account-deploy

# SSM Parameter Store
ssm:
  source_region: us-east-1
  sync_tag_key: SyncAcrossRegions

# Route53 Configuration
route53:
  zone_id: Z1763QDC3XWVS7
  records:
    
elasticache:
  - template:
      location: 'templates/elasticache.json.j2'
      variables:
        target_cluster_name: mart-data-v7-cluster-dev
        target_cluster_description: Mart Data Cache
        node_group_configuration:
          - Slots: 0-8191
            ReplicaCount: 1
          - Slots: 8192-16383
            ReplicaCount: 1
        cache_parameter_group: default.redis7.cluster.on
        engine_version: "7.0"
        nodenumber: 2
        need_data_backup: true
        data_backup_prefix: redis-data-snapshot/automatic.mart-data-v7-cluster-dev
        s3_bucket_name: mart-data-dev-dr
        cache_node_type: cache.r7g.xlarge
        security_group_ids:
          - sg-03aae3fda738ccd3f
          - sg-0cb0b481497065818
  - template:
      location: 'templates/elasticache.json.j2'
      variables:
        target_cluster_name: application-cache-v7-cluster-dev
        target_cluster_description: mart application cache
        source_region: us-east-1
        node_group_configuration:
          - Slots: 0-5461
          - Slots: 5462-10922
          - Slots: 10923-16383
        cache_parameter_group: default.redis7.cluster.on
        engine_version: "7.0"
        nodenumber: 3
        need_data_backup: false
        cache_node_type: cache.t3.small
        security_group_ids:
          - sg-03aae3fda738ccd3f
          - sg-0cb0b481497065818
  - template:
      location: 'templates/elasticache.json.j2'
      variables:
        target_cluster_name: ts-application-cache-v7-cluster-dev
        target_cluster_description: redis cache cluster for TS data
        node_group_configuration:
          - Slots: 0-16383
        cache_parameter_group: default.redis7.cluster.on
        nodenumber: 1
        engine_version: "7.1"
        need_data_backup: false
        cache_node_type: cache.t3.small
        security_group_ids:
          - sg-03aae3fda738ccd3f
          - sg-0cb0b481497065818