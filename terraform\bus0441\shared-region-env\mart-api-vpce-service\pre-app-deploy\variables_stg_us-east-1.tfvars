region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "stg"
environmentForTagging = "stg"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

mart_api_nlb_arn         = "arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/net/mart-api-nlb-stg/cbeec809312d60ee"
has_vpce_service         = true
vpce_service_private_dns = "investmentapi-stg.dpd8eb6b.easn.morningstar.com"
vpce_service_allowed_principals = [
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::334850792636:root",
  "arn:aws:iam::864899861782:root",
  "arn:aws:iam::553297463534:root",
  "arn:aws:iam::021033229982:root"
]
vpce_subnet_ids = [
  "subnet-038ca9497049287b6", # us-east-1a-private
  "subnet-02a941119c6855102"  #	us-east-1b-private
]
