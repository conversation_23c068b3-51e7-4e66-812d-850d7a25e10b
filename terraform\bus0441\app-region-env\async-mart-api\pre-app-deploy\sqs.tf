resource "aws_sqs_queue" "async-mart-api-message" {
    name                               = "async-mart-api-message-${var.environment}"
    visibility_timeout_seconds         = 30
    max_message_size                   = 262144
    message_retention_seconds          = 86400
    receive_wait_time_seconds          = 20
    tags                               = local.tags
    redrive_policy = jsonencode({
                             deadLetterTargetArn = aws_sqs_queue.async-mart-api-message-dlq.arn
                             maxReceiveCount     = 3
    })
}

resource "aws_sqs_queue" "async-mart-api-message-dlq" {
    name                               = "async-mart-api-message-${var.environment}-dlq"
    tags                               = local.tags
}

data "aws_iam_policy_document" "allow-async-input-queue-read-policy" {
    statement {
        effect = "Allow"
        sid = "async-mart-api-message-queue-access"
        principals {
            type = "AWS"
            identifiers = ["*"]
        }
        actions = [
            "SQS:*"
        ]
        resources = [
            "arn:aws:sqs:*:*:${aws_sqs_queue.async-mart-api-message.name}"
        ]
        condition {
            test = "ArnLike"
            variable = "aws:SourceArn"
            values   = [aws_s3_bucket.async-mart-api-input.arn]
        }
    }
}

resource "aws_sqs_queue_policy" "allow-message-from-input-bucket" {
    queue_url = aws_sqs_queue.async-mart-api-message.id
    policy    = data.aws_iam_policy_document.allow-async-input-queue-read-policy.json
}

resource "aws_sqs_queue_redrive_allow_policy" "async-mart-api-message-dlq-permissions-policy" {
    queue_url = aws_sqs_queue.async-mart-api-message-dlq.id
    redrive_allow_policy = jsonencode({
        redrivePermission = "byQueue",
        sourceQueueArns   = [aws_sqs_queue.async-mart-api-message.arn]
    })
}

resource "aws_sqs_queue" "ph-async-mart-api-message" {
    name                               = "ph-async-mart-api-message-${var.environment}"
    visibility_timeout_seconds         = 30
    max_message_size                   = 262144
    message_retention_seconds          = 86400
    receive_wait_time_seconds          = 20
    tags                               = local.tags
    redrive_policy = jsonencode({
        deadLetterTargetArn = aws_sqs_queue.ph-async-mart-api-message-dlq.arn
        maxReceiveCount     = 3
    })
}

resource "aws_sqs_queue" "ph-async-mart-api-message-dlq" {
    name                               = "ph-async-mart-api-message-${var.environment}-dlq"
    tags                               = local.tags
}

data "aws_iam_policy_document" "allow-ph-async-input-queue-read-policy" {
    statement {
        effect = "Allow"
        sid = "ph-async-mart-api-message-queue-access"
        principals {
            type = "AWS"
            identifiers = ["*"]
        }
        actions = [
            "SQS:*"
        ]
        resources = [
            "arn:aws:sqs:*:*:${aws_sqs_queue.ph-async-mart-api-message.name}"
        ]
        condition {
            test = "ArnLike"
            variable = "aws:SourceArn"
            values   = [aws_s3_bucket.async-mart-api-input.arn]
        }
    }
}

resource "aws_sqs_queue_policy" "ph-allow-message-from-input-bucket" {
    queue_url = aws_sqs_queue.ph-async-mart-api-message.id
    policy    = data.aws_iam_policy_document.allow-ph-async-input-queue-read-policy.json
}

resource "aws_sqs_queue_redrive_allow_policy" "ph-async-mart-api-message-dlq-permissions-policy" {
    queue_url = aws_sqs_queue.ph-async-mart-api-message-dlq.id
    redrive_allow_policy = jsonencode({
        redrivePermission = "byQueue",
        sourceQueueArns   = [aws_sqs_queue.ph-async-mart-api-message.arn]
    })
}