resource "harness_platform_triggers" "pr_to_develop" {
  count       = var.triggers.pr_to_develop ? 1 : 0
  identifier  = "pr_to_develop"
  name        = "PR-to-develop"
  org_id      = var.org_id
  project_id  = var.project_id
  target_id   = var.pipeline_id

  yaml        = templatefile("${path.module}/templates/triggers/pr_branch_trigger.tftpl",
    {
      org_id        = var.org_id
      project_id    = var.project_id
      pipeline_id   = var.pipeline_id
      repo_name     = var.repo_name
      trigger_id    = "pr_to_develop"
      trigger_name  = "PR-to-develop"
      operator      = "Equals"
      branch        = "develop"
      input_set_id  = "pr_to_input_set"
    }
  )
  depends_on = [harness_platform_input_set.this]
}

resource "harness_platform_triggers" "pr_to_release" {
  count       = var.triggers.pr_to_release ? 1 : 0
  identifier  = "pr_to_release"
  name        = "PR-to-release"
  org_id      = var.org_id
  project_id  = var.project_id
  target_id   = var.pipeline_id
  yaml        = templatefile("${path.module}/templates/triggers/pr_branch_trigger.tftpl",
    {
      org_id        = var.org_id
      project_id    = var.project_id
      pipeline_id   = var.pipeline_id
      repo_name     = var.repo_name
      trigger_id    = "pr_to_release"
      trigger_name  = "PR-to-release"
      operator      = "StartsWith"
      branch        = "release/"
      input_set_id  = "pr_to_input_set"
    }
  )
  depends_on = [harness_platform_input_set.this]
}

resource "harness_platform_triggers" "pr_to_master" {
  count       = var.triggers.pr_to_master ? 1 : 0
  identifier  = "pr_to_master"
  name        = "PR-to-master"
  org_id      = var.org_id
  project_id  = var.project_id
  target_id   = var.pipeline_id
  yaml        = templatefile("${path.module}/templates/triggers/pr_branch_trigger.tftpl",
    {
      org_id        = var.org_id
      project_id    = var.project_id
      pipeline_id   = var.pipeline_id
      repo_name     = var.repo_name
      trigger_id    = "pr_to_master"
      trigger_name  = "PR-to-master"
      operator      = "Equals"
      branch        = "master"
      input_set_id  = "pr_to_input_set"
    }
  )
  depends_on = [harness_platform_input_set.this]
}