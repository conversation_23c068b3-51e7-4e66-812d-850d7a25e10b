resource "aws_lb_target_group" "mart_api_ecs_target_group_1" {
  name                          = "mart-api-tg-ecs-${var.environment}-1"
  target_type                   = "ip"
  port                          = 8080
  protocol                      = "HTTP"
  vpc_id                        = var.vpc
  load_balancing_algorithm_type = "least_outstanding_requests"

  health_check {
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTP"
    matcher             = "200-399"
    timeout             = "3"
    path                = "/investment-api/v1/health-check"
    unhealthy_threshold = "3"
  }
  tags = local.tags
}

resource "aws_lb_target_group" "mart_api_ecs_target_group_2" {
  name                          = "mart-api-tg-ecs-${var.environment}-2"
  target_type                   = "ip"
  port                          = 8080
  protocol                      = "HTTP"
  vpc_id                        = var.vpc
  load_balancing_algorithm_type = "least_outstanding_requests"

  health_check {
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTP"
    matcher             = "200-399"
    timeout             = "3"
    path                = "/investment-api/v1/health-check"
    unhealthy_threshold = "3"
  }
  tags = local.tags
}



// reroute the traffic to the target group based on the host header, need
// to include both mart-api and investment-api in the host header
// these resources are moved from post-app-deploy to pre-app-deploy, meaning we need to reimport
// these resources before any deployment
resource "aws_lb_listener_rule" "mart_api_host_based_routing_blue" {
  listener_arn = aws_lb_listener.https443.arn
  priority     = 100
  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.mart_api_ecs_target_group_1.arn
  }
  condition {
    # Removed host_header since alb health-point check will not have a host header and
    # Harness can only have one listener_rule for BlueGreen deployments
    path_pattern {
      values = [
        "/*"
      ]
    }
  }

  #noinspection HILUnresolvedReference
  lifecycle {
    #noinspection HILUnresolvedReference
    ignore_changes = [
      # Ignoring because harness will change the value during a B/G swap
      action[0].target_group_arn
    ]
  }

  tags = merge(local.tags, {
    NAME = "Blue"
  })
}

resource "aws_lb_listener_rule" "mart_api_host_based_routing_green" {
  count        = var.use_bg_deployment ? 1 : 0
  listener_arn = aws_lb_listener.https8443[0].arn
  priority     = 101
  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.mart_api_ecs_target_group_2.arn
  }
  condition {
    # Removed host_header since alb health-point check will not have a host header and
    # Harness can only have one listener_rule for BlueGreen deployments
    path_pattern {
      values = [
        "/*"
      ]
    }
  }

  lifecycle {
    #noinspection HILUnresolvedReference
    ignore_changes = [
      # Ignoring because harness will change the value during a B/G swap
      action[0].target_group_arn
    ]
  }

  tags = merge(local.tags, {
    NAME = "Green"
  })
}