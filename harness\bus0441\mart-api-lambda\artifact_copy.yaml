name: Copy Artifacts to S3
items:
  - sourceFile: target/mart-api-lambda.jar
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: dpda-deploy-nonprod-us-east-1
        key: mart-api-lambda/{{version}}/app.jar
      - accountId: "************"
        region: eu-west-1
        bucket: dpda-deploy-nonprod-eu-west-1
        key: mart-api-lambda/{{version}}/app.jar
  - sourceFile: function.zip
    targets:
      - accountId: "************"
        region: us-east-1
        bucket: dpda-deploy-nonprod-us-east-1
        key: mart-api-lambda/{{version}}/function.zip
      - accountId: "************"
        region: eu-west-1
        bucket: dpda-deploy-nonprod-eu-west-1
        key: mart-api-lambda/{{version}}/function.zip
