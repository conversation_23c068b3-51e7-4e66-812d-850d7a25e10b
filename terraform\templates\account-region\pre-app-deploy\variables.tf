variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "ecr_repos" {
  type = list(object({
    repoName           = string
    serviceIdLookupKey = string
  }))
}

variable "serviceIdMap" {
  type = map(string)
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

locals {
  tags = {
    MANAGED     = "terraform"
    TID         = "DPDA"
    ENVIRONMENT = var.environmentForTagging
  }
}