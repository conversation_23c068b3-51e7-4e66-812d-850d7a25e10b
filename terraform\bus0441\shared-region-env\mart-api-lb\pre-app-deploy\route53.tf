resource "aws_route53_record" "service-mstar" {
  count   = var.primary_route_53_required ? 1 : 0
  zone_id = data.aws_route53_zone.zone.zone_id
  name    = "martapi-${var.environment}.${data.aws_route53_zone.zone.name}"
  type    = "CNAME"
  ttl     = "300"

  weighted_routing_policy {
    weight = 1
  }

  set_identifier = "${var.environment}-${var.region}"
  records        = [aws_lb.mart_api_alb.dns_name]
}

resource "aws_route53_record" "service-mstar-dr" {
  count   = var.region == "us-west-2" ? 1 : 0
  zone_id = data.aws_route53_zone.zone.zone_id
  name    = "martapi-${var.environment}.${data.aws_route53_zone.zone.name}"
  type    = "CNAME"
  ttl     = "300"

  weighted_routing_policy {
    weight = 0
  }

  set_identifier = "${var.environment}-dr-${var.region}"
  records        = [aws_lb.mart_api_alb.dns_name]
}

resource "aws_route53_record" "service-mstar-region-based" {
  zone_id = data.aws_route53_zone.zone.zone_id
  name    = "martapi-${var.environment}-${var.region}.${data.aws_route53_zone.zone.name}"
  type    = "CNAME"
  ttl     = "300"

  weighted_routing_policy {
    weight = 1
  }

  set_identifier = "${var.environment}-${var.region}"
  records        = [aws_lb.mart_api_alb.dns_name]
}
