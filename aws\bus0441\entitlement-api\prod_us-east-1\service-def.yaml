launchType: FARGATE
serviceName: prod-entitlement-api-service-nlb
desiredCount: 2
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-04bff8d7169894570 # private_app
      - sg-08913413a8f7f1a9d # private_web
      - sg-0768ac9a5f89d182d # private_db
    subnets:
      - subnet-0dcfedb0a8fe2f138 # us-east-1c-private
      - subnet-0d610a03d59c8889d # us-east-1b-private
      - subnet-0c3db8d6d567a4f4a # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 300

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: prod-entitlement-api
    containerPort: 8443

tags:
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: ORG
    value: <+serviceVariables.ORG>
  - key: TID
    value: <+variable.TID>
  - key: PID
    value: <+serviceVariables.PID>
  - key: MANAGED
    value: <+serviceVariables.MANAGED>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>

propagateTags: TASK_DEFINITION