variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

variable "release_version" {
  type = string
}
##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

locals {
  tags = {
    SERVICEID   = "ts01894"
    MANAGED     = "terraform"
    PID         = "PID0462"
    TID         = "DPDA"
    FUNCTION    = "app"
    ENVIRONMENT = var.environmentForTagging
  }
  lambda_s3_bucket = "dpda-deploy-${var.account_env}-${var.region}"
}

variable "vpc" {
  description = "vpc"
}

variable "mart_api_nlb_name" {
  description = "load balancer used by mart-api"
}

variable "environment_suffix" {
  description = "The environment"
}

variable "account_env" {
}
variable "required" {
  default = 0
}

variable "account_id" {
  description = "AWS Account Id"
}

variable "dataac_account_id" {
  description = "DATAAC AWS Account Id"
}

variable "subscriber_aws_ids" {
  type        = list(string)
  description = "One or more AWS ids to be subscribed to sns topic"
}

variable "us-east-1" {
  type    = string
  default = "us-east-1"
}

variable "eu-west-1" {
  type    = string
  default = "eu-west-1"
}

variable "required_replica" {
  default = 0
}
