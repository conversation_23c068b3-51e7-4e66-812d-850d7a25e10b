launchType: FARGATE
serviceName: ph-mart-api-fargate-nlb-dev
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0a862c74582ddf040 # private_app
      - sg-0220dde95ed0e2884 # private_web
      - sg-0df8f5f2425904528 # private_db
      - sg-07349787f4c674600 # vpce-mongodb-client
      #- sg-0855fcdf523d95fad # private_internal_email_morningstar
    subnets:
      - subnet-02eb4a4d13d029c56 # eu-west-1c-private
      - subnet-0c7ad2143f5e9a8dd # eu-west-1b-private
      - subnet-05833c296327b5241 # eu-west-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent: 200
  minimumHealthyPercent: 0

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
  #- targetGroupArn: <+targetGroupArn> # This expression is used by Harness native B/G deployment step
    containerName: ph-mart-api-fargate-container-dev
    containerPort: 8080

tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION