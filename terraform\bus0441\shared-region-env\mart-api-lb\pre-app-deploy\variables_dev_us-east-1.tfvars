region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "dev"
environmentForTagging = "dev"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

domain_name               = "*.date7ebe.easn.morningstar.com"
mart_api_nlb_name         = "mart-api-nlb-stg"
vpc                       = "vpc-01453a03f6929cb2e"
zone_id                   = "dpd8eb6b.easn.morningstar.com"
primary_route_53_required = true
use_bg_deployment         = true
use_tscache_listener_rule = true
use_ph_listener_rule      = true
use_tscache_grpc_listener_rule = true