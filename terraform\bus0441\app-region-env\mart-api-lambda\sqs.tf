resource "aws_sqs_queue" "rdb_sqs" {
  name                        = "rdb-sqs-${var.environment}"
  visibility_timeout_seconds  = 601
  delay_seconds               = 0
  max_message_size            = 262144
  message_retention_seconds   = 345600
  receive_wait_time_seconds   = 20
  tags = local.tags
}

resource "aws_sns_topic_subscription" "rdb_updates_sqs_target" {
  count = var.required
  topic_arn = var.rdb_topic_sqs
  protocol  = "sqs"
  endpoint  = "${aws_sqs_queue.rdb_sqs.arn}"
  raw_message_delivery = "true"
}

resource "aws_sqs_queue_policy" "rdb_sqs_access_control" {
  queue_url = aws_sqs_queue.rdb_sqs.id

  policy = <<POLICY
  {
  "Version": "2012-10-17",
  "Id": "${aws_sqs_queue.rdb_sqs.arn}/SQSDefaultPolicy",
  "Statement": [
    {
      "Sid": "__owner_statement",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      },
      "Action": "SQS:*",
      "Resource": "${aws_sqs_queue.rdb_sqs.arn}"
    },
    {
      "Sid": "__sender_statement",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::************:root"
      },
      "Action": "SQS:SendMessage",
      "Resource": "${aws_sqs_queue.rdb_sqs.arn}"
    },
    {
      "Sid": "topic-subscription-${var.rdb_topic_sqs}",
      "Effect": "Allow",
      "Principal": {
        "AWS": "*"
      },
      "Action": "SQS:SendMessage",
      "Resource": "${aws_sqs_queue.rdb_sqs.arn}",
      "Condition": {
        "ArnLike": {
          "aws:SourceArn": "${var.rdb_topic_sqs}"
        }
      }
    }
  ]
}
POLICY
}

