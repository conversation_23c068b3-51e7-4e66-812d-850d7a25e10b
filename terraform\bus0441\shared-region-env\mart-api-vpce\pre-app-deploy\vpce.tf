resource "aws_vpc_endpoint" "custom_calc_vpce" {
  count             = var.has_custom_calc_vpce
  vpc_id            = module.region_data.vpc_id[0]
  service_name      = var.custom_calc_vpce_service_name
  vpc_endpoint_type = "Interface"

  security_group_ids = [
    module.region_data.security_group_private_app[0],
    module.region_data.security_group_private_web[0]
  ]

  subnet_ids = var.vpce_subnet_ids

  tags = merge(local.tags, { Name : "CCS API" })
}


resource "aws_vpc_endpoint" "rdb_vpce" {
  count             = var.required_rdb_vpce
  vpc_id            = module.region_data.vpc_id[0]
  service_name      = var.rdb_vpce_service_name
  vpc_endpoint_type = "Interface"

  security_group_ids = [
    module.region_data.security_group_private_db[0],
    module.region_data.security_group_private_app[0],
    module.region_data.security_group_private_web[0]
  ]

  subnet_ids = var.vpce_subnet_ids

  tags = merge(local.tags, { Name : var.rdb_vpce_name })
}

resource "aws_vpc_endpoint" "mart_api_vpce" {
  count             = var.has_mart_api_vpce_service
  vpc_id            = module.region_data.vpc_id[0]
  service_name      = var.mart_api_vpce_service_name
  vpc_endpoint_type = "Interface"
  subnet_ids        = var.vpce_subnet_ids
  security_group_ids = [
    module.region_data.security_group_private_app[0],
    module.region_data.security_group_private_web[0]
  ]
  tags = merge(local.tags, { Name : "mart-api-endpoint-${var.environmentForTagging}" })

}