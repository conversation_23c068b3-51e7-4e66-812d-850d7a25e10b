region                = "eu-west-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "prod"
environmentForTagging = "prd"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

zone_id                    = "dpd6b927.eas.morningstar.com"
has_mart_api_vpce_service  = 1
mart_api_vpce_service_name = "com.amazonaws.vpce.eu-west-1.vpce-svc-0a16ed56bfb6f1a70"
required_rdb_vpce          = 0
vpce_subnet_ids = [
  "subnet-09ccfc3620f652857", # eu-west-1a-private
  "subnet-028c31b8c943d1a56"  #	eu-west-1b-private
]