// need to make sure these resources deployed before the listener rules in mart-api app level.
resource "aws_lb" "mart_api_alb" {
  name               = "mart-api-alb-${var.environment}"
  internal           = true
  load_balancer_type = "application"
  security_groups    = flatten([module.region_data.security_group_private_web])
  subnets            = flatten([
    module.region_data.private_subnet_1_id,
    module.region_data.private_subnet_2_id,
    module.region_data.private_subnet_3_id
  ])
  tags = local.tags
}

resource "aws_lb_listener" "https443" {
  load_balancer_arn = aws_lb.mart_api_alb.arn
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = data.aws_acm_certificate.mart_acm_certificate.arn
  default_action {
    type = "fixed-response"
    fixed_response {
      content_type = "text/plain"
      status_code  = "404"
      message_body = "Page Not Found"
    }
  }
  tags = local.tags
}

resource "aws_lb_listener" "https8443" {
  count             = var.use_bg_deployment ? 1 : 0
  load_balancer_arn = aws_lb.mart_api_alb.arn
  port              = 8443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = data.aws_acm_certificate.mart_acm_certificate.arn
  default_action {
    type = "fixed-response"
    fixed_response {
      content_type = "text/plain"
      status_code  = "404"
      message_body = "Page Not Found"
    }
  }
  tags = local.tags
}

# TODO Add a 8443 listener for staging area

resource "aws_lb_listener" "http80" {
  load_balancer_arn = aws_lb.mart_api_alb.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      protocol    = "HTTPS"
      port        = "443"
      status_code = "HTTP_301"
    }
  }
}
