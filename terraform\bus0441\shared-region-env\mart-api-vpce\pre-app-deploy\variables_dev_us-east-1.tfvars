region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "dev"
environmentForTagging = "dev"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

zone_id               = "dpd8eb6b.easn.morningstar.com"
required_rdb_vpce     = 1
rdb_vpce_name         = "rdb-nonprod-ro"
rdb_vpce_service_name = "com.amazonaws.vpce.us-east-1.vpce-svc-0923cd5703098c8ae"
vpce_subnet_ids = [
  "subnet-038ca9497049287b6", # us-east-1a-private
  "subnet-02a941119c6855102"  #	us-east-1b-private
]
