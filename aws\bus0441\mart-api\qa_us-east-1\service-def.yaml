launchType: FARGATE
serviceName: mart-api-fargate-nlb-qa
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0d39ed658896db12f # private_app
      - sg-0c2f63f87086d9c2b # private_web
      - sg-03f1c64fc9d6fc7eb # private_db
      - sg-000617eb55d00dfa4 # vpce-mongodb-client      
      #- sg-0855fcdf523d95fad # private_internal_email_morningstar
    subnets:
      - subnet-039898dae88d5b5cb # us-east-1c-private
      - subnet-02a941119c6855102 # us-east-1b-private
      - subnet-038ca9497049287b6 # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 50

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: mart-api-fargate-container-qa
    containerPort: 8080
enableExecuteCommand: true
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION