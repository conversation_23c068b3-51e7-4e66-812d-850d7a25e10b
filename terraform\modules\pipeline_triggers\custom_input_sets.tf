resource "harness_platform_input_set" "this" {
  count       = var.custom_input_sets == null ? 0 : length(var.custom_input_sets)

  identifier  = var.custom_input_sets[count.index].identifier
  name        = var.custom_input_sets[count.index].name
  tags        = ["terraform_managed"]
  org_id      = var.org_id
  project_id  = var.project_id
  pipeline_id = var.pipeline_id
  yaml        = templatefile(var.custom_input_sets[count.index].template.file_path,
                  merge(var.custom_input_sets[count.index].template.variables, {
                    org_id      = var.org_id
                    project_id  = var.project_id
                    pipeline_id = var.pipeline_id
                    identifier  = var.custom_input_sets[count.index].identifier
                    name        = var.custom_input_sets[count.index].name
                  })
                )
}
