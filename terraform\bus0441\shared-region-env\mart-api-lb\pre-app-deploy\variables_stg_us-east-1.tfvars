region                = "us-east-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "stg"
environmentForTagging = "stg"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

domain_name               = "*.date7ebe.easn.morningstar.com"
mart_api_nlb_name         = "mart-api-nlb-stg"
vpc                       = "vpc-01453a03f6929cb2e"
zone_id                   = "dpd8eb6b.easn.morningstar.com"
required                  = 1
primary_route_53_required = true
use_bg_deployment         = true
use_tscache_listener_rule = true
use_ph_listener_rule      = true
use_tscache_grpc_listener_rule = true
mart-api-tg-nlb-dev-arn   = "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/mart-api-tg-nlb-dev/d7090c7b86c64429"
mart-api-tg-nlb-qa-arn    = "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/mart-api-tg-nlb-qa/317996637ec1e1fe"