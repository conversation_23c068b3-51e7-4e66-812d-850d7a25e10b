variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "required" {
  default = 0
}

variable "velo_role" {
  default     = ""
  description = "The role in dpda need to assume"
}
variable "redshift_assume_role" {
  default     = ""
  description = "The role in redshift account need to assume"
}

variable "account_id" {
  default     = ""
  description = "The account id of the resources"
}

variable "asyncdata_feed_worker" {
  default     = ""
  description = "The role of delta feed builder that need permission to assume redshift role"
}

variable "asyncdata_feed_worker_dr" {
  default     = ""
  description = "The role of delta feed builder that need permission to assume redshift role in DR region"
}

variable "velo_test_role" {
  default     = ""
  description = "The role of velo regression test that needs permission to assume redshift role"
}

variable "dataac_lambda_role" {
  default     = ""
  description = "The role of lambda execution for DynamoDB replication"
}
