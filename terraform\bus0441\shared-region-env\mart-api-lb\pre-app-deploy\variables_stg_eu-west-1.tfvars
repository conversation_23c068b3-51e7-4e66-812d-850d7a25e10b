region                = "eu-west-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "stg"
environmentForTagging = "stg"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

domain_name               = "*.date7ebe.easn.morningstar.com"
mart_api_nlb_name         = "mart-api-nlb-stg"
vpc                       = "vpc-0f533871a3f31c658"
zone_id                   = "dpd8eb6b.easn.morningstar.com"
required                  = 1
use_bg_deployment         = true
use_tscache_listener_rule = false
use_ph_listener_rule      = true