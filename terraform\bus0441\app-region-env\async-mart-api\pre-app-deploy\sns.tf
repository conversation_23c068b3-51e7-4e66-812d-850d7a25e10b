resource "aws_sns_topic" "user_updates" {
  name = "async-mart-api-job-notification-${var.environment}"
  tags = local.tags
}

resource "aws_sns_topic" "asyncapi_notif" {
  name  = "async-mart-api-overflow-notif-${var.environment}"
  count = var.required
  tags  = local.tags
}

resource "aws_sns_topic_subscription" "email-subscription" {
  topic_arn = aws_sns_topic.asyncapi_notif[count.index].arn
  count     = var.required
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_subscription" "vo-subscription" {
  topic_arn = aws_sns_topic.asyncapi_notif[count.index].arn
  count     = var.required == 1 ? var.environment == "prod" ? 1 : 0 : 0
  protocol  = "https"
  endpoint  = "https://alert.victorops.com/integrations/cloudwatch/20131130/alert/3da4a2a2-477d-4cdd-8dab-1d1e76c25a3d/Ts01695-invAPI-HoldingAsyncWorker"
}

resource "aws_sns_topic_policy" "default" {
  arn = aws_sns_topic.user_updates.arn

  policy = data.aws_iam_policy_document.sns_topic_policy.json
}

data "aws_iam_policy_document" "sns_topic_policy" {
  policy_id = "__default_policy_ID"

  statement {
    actions = [
      "SNS:GetTopicAttributes",
      "SNS:SetTopicAttributes",
      "SNS:AddPermission",
      "SNS:RemovePermission",
      "SNS:DeleteTopic",
      "SNS:Subscribe",
      "SNS:ListSubscriptionsByTopic",
      "SNS:Publish"
    ]

    condition {
      test     = "StringEquals"
      variable = "AWS:SourceOwner"

      values = [
        var.account_id,
      ]
    }

    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    resources = [
      aws_sns_topic.user_updates.arn,
    ]

    sid = "__default_statement_ID"
  }

  statement {
    sid    = "__console_sub_0"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = flatten([var.subscriber_aws_ids])
    }
    actions = ["SNS:Subscribe"]
    resources = [
      aws_sns_topic.user_updates.arn
    ]
  }
}