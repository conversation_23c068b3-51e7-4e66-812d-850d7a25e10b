launchType: FARGATE
serviceName: tscache-mart-api-fargate-nlb-dev
desiredCount: 1
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0d39ed658896db12f # private_app
      - sg-0c2f63f87086d9c2b # private_web
      - sg-03f1c64fc9d6fc7eb # private_db
      #- sg-009b17a4e9b81a5f0 # private_internal_email_morningstar
    subnets:
      - subnet-039898dae88d5b5cb # us-east-1c-private
      - subnet-02a941119c6855102 # us-east-1b-private
      - subnet-038ca9497049287b6 # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 0

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
  #- targetGroupArn: <+targetGroupArn> # This expression is used by Harness native B/G deployment step
    containerName: tscache-mart-api-fargate-container-dev
    containerPort: 8080

tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION