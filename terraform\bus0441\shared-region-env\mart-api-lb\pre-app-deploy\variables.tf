variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "domain_name" {
  description = "mart-api domain name"
}

variable "mart_api_nlb_name" {
  description = "network load balancer used by mart-api"
}

variable "vpc" {
  description = "vpc"
}

variable "zone_id" {
  description = "route-53 zone domain name"
}

variable "required" {
  default = 0
}

variable "primary_route_53_required" {
  default = false
}

variable "use_bg_deployment" {
  default = false
}

variable "use_tscache_listener_rule" {
  default = true
}

variable "use_ph_listener_rule" {
  default = true
}

variable "use_tscache_grpc_listener_rule" {
  default = true
}

variable "mart-api-tg-nlb-dev-arn" {
  type = string
  default = ""
}

variable "mart-api-tg-nlb-qa-arn" {
  type = string
  default = ""
}

locals {
  tags = {
    SERVICEID   = "ts01695"
    MANAGED     = "terraform"
    PID         = "PID0462"
    TID         = "DPDA"
    ORG         = "MAAS"
    ENVIRONMENT = var.environmentForTagging
  }
}
