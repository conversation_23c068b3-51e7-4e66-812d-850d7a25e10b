launchType: FARGATE
serviceName: ph-async-api-service-prod
desiredCount: 0
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0e0437a4ad0633a99  #private_web
      - sg-0e59dd6a8457e658d  #private_app
      - sg-0ab3f01d10c8c197a  #private_db
      - sg-07059e3f01023768b #private_mongo
    subnets:
      - subnet-033bddf5b679971ba # eu-west-1c-private
      - subnet-028c31b8c943d1a56 # eu-west-1b-private
      - subnet-09ccfc3620f652857 # eu-west-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 100
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION