launchType: FARGATE
serviceName: dev-entitlement-api-service-nlb
desiredCount: 2
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0d39ed658896db12f # private_app
      - sg-0c2f63f87086d9c2b # private_web
      - sg-0d39ed658896db12f # private_db
    subnets:
      - subnet-039898dae88d5b5cb # us-east-1c-private
      - subnet-02a941119c6855102 # us-east-1b-private
      - subnet-038ca9497049287b6 # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 300

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: dev-entitlement-api
    containerPort: 8443

tags:
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: ORG
    value: <+serviceVariables.ORG>
  - key: TID
    value: <+variable.TID>
  - key: PID
    value: <+serviceVariables.PID>
  - key: MANAGED
    value: <+serviceVariables.MANAGED>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>

propagateTags: TASK_DEFINITION
