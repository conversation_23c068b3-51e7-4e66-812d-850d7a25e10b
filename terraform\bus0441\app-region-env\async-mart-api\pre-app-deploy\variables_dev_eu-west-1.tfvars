region                = "eu-west-1"
role_to_assume        = "arn:aws:iam::************:role/mstar-engr-cross-account-deploy"
environment           = "dev"
environmentForTagging = "dev"

##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

vpc                = "vpc-0f533871a3f31c658"
mart_api_nlb_name  = "mart-api-nlb-stg"
environment_suffix = "dev-eu"
account_env        = "nonprod"
account_id         = "************"
dataac_account_id  = "************"
subscriber_aws_ids = [
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root",
  "arn:aws:iam::************:root"
]