variable "org_id" {
  type = string
}

variable "project_id" {
  type = string
}

variable "pipeline_id" {
  type        = string
  description = "Harness Pipeline Id"
}

variable "repo_name" {
  type        = string
  description = "bitbucket repo e.g. dataac/mart-api"
}

variable "custom_input_sets" {
  type = list(object({
    identifier = string
    name       = string
    template = object({
      file_path = string
      variables = map(any)
    })
  }))
  nullable = true
  default  = null
}

variable "custom_triggers" {
  type = list(object({
    identifier = string
    name       = string
    template = object({
      file_path = string
      variables = map(any)
    })
  }))
  nullable = true
  default  = null
}

variable "triggers" {
  type = object({
    pr_to_develop   = bool
    pr_to_release   = bool
    pr_to_master    = bool
    push_to_master  = bool
    push_to_develop = bool
  })
  default = {
    pr_to_develop   = true
    pr_to_release   = true
    pr_to_master    = true
    push_to_master  = true
    push_to_develop = true
  }
}
