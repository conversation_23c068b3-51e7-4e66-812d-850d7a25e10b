launchType: FARGATE
serviceName: async-api-service-prod
desiredCount: 2
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-04bff8d7169894570 # private_app
      - sg-08913413a8f7f1a9d # private_web
      - sg-0768ac9a5f89d182d # private_db
      - sg-05d68293359ab5f05 #private_mongo
    subnets:
      - subnet-0dcfedb0a8fe2f138 # us-east-1c-private
      - subnet-0d610a03d59c8889d # us-east-1b-private
      - subnet-0c3db8d6d567a4f4a # us-east-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent": 200
  minimumHealthyPercent: 100
tags: # only used during creation of ECS service
  - key: BSID
    value: <+variable.BSID>
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: FUNCTION
    value: <+serviceVariables.FUNCTION>
  - key: TID
    value: <+variable.TID>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>
propagateTags: TASK_DEFINITION