resource "aws_lambda_function" "asyncapi-scaling-lambda" {
  function_name     = "asyncapi-scaling-lambda-${var.environment}"
  s3_bucket         = local.lambda_s3_bucket
  s3_key            = "asyncapi-scaling-lambda/${var.release_version}/app.jar"
  description       = "A lambda function for scaling asyncapi service"
  role              = data.aws_iam_role.mart_role.arn
  runtime           = "java17"
  handler           = "com.morningstar.asyncapi.lambda.LambdaService::handleMessages"
  timeout           = 300
  memory_size       = 256
  tags              = local.tags

  vpc_config {
    subnet_ids = [
      module.region_data.private_subnet_1_id[0],
      module.region_data.private_subnet_2_id[0],
      module.region_data.private_subnet_3_id[0]
    ]

    security_group_ids = [
      module.region_data.security_group_private_app[0],
      module.region_data.security_group_private_web[0]
    ]
  }

  environment {
    variables = {
      Environment           = var.environment
      async_profiles_active = var.environment_suffix
    }
  }

  depends_on = [
    aws_cloudwatch_log_group.asyncapi_scaling_lambda_log
  ]
}


resource "aws_cloudwatch_log_group" "asyncapi_scaling_lambda_log" {
  name              = "/aws/lambda/asyncapi-scaling-lambda-${var.environment}"
  retention_in_days = 30
  tags              = local.tags
}

resource "aws_cloudwatch_log_subscription_filter" "asyncapi_scaling_lambda_log_subscription" {
  name            = "asyncapi-scaling-lambda-log-subscription-${var.environment}"
  log_group_name  = aws_cloudwatch_log_group.asyncapi_scaling_lambda_log.name
  filter_pattern  = ""
  destination_arn = data.aws_kinesis_stream.stream.arn
  role_arn        = data.aws_iam_role.log_role.arn
}
