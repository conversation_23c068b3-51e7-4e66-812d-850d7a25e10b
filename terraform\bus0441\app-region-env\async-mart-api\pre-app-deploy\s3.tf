resource "aws_s3_bucket" "async-mart-api-input" {
    bucket = "dpda-async-mart-api-input-${var.environment_suffix}"
    tags = local.tags
}

resource "aws_s3_bucket" "async-mart-api-output" {
    bucket = "dpda-async-mart-api-output-${var.environment_suffix}"
    tags = local.tags
}

resource "aws_s3_bucket_ownership_controls" "async-mart-api-input-ownership-controls" {
    bucket = aws_s3_bucket.async-mart-api-input.id
    rule {
        object_ownership = "BucketOwnerEnforced"
    }
}

resource "aws_s3_bucket_ownership_controls" "async-mart-api-output-ownership-controls" {
    bucket = aws_s3_bucket.async-mart-api-output.id
    rule {
        object_ownership = "BucketOwnerEnforced"
    }
}

resource "aws_s3_bucket_versioning" "async-mart-api-input-versioning" {
    bucket = aws_s3_bucket.async-mart-api-input.id
    versioning_configuration {
        status = "Enabled"
    }
}

resource "aws_s3_bucket_versioning" "async-mart-api-output-versioning" {
    bucket = aws_s3_bucket.async-mart-api-output.id
    versioning_configuration {
        status = "Enabled"
    }
}

resource "aws_s3_bucket_lifecycle_configuration" "async-mart-api-input-lifecycle" {
    bucket = aws_s3_bucket.async-mart-api-input.id

    rule {
        id = "async-api-input-expiration"
        expiration {
            days = 15
        }
        status = "Enabled"
    }
}

resource "aws_s3_bucket_lifecycle_configuration" "async-mart-api-output-lifecycle" {
    bucket = aws_s3_bucket.async-mart-api-output.id

    rule {
        id = "async-api-output-expiration"
        expiration {
            days = 15
        }
        noncurrent_version_expiration {
            noncurrent_days = 7
        }
        status = "Enabled"
    }
}

resource "aws_s3_bucket_notification" "async-api-input-bucket-notification" {
    bucket = aws_s3_bucket.async-mart-api-input.id
    queue {
        queue_arn = aws_sqs_queue.async-mart-api-message.arn
        events = ["s3:ObjectCreated:*"]
        filter_prefix = "gridview/"
    }
    queue {
        queue_arn = aws_sqs_queue.ph-async-mart-api-message.arn
        events = ["s3:ObjectCreated:*"]
        filter_prefix = "ph/"
    }
    depends_on = [
        aws_sqs_queue_policy.allow-message-from-input-bucket
    ]
}

resource "aws_s3_bucket_policy" "async-mart-api-output-policy" {
    bucket = aws_s3_bucket.async-mart-api-output.id

    policy = jsonencode({
        Version = "2012-10-17"
        Id      = "PolicyForDestinationBucket"
        Statement = [
            {
                Sid    = "Permissions on objects"
                Effect = "Allow"
                Principal = {
                    AWS = "arn:aws:iam::${var.dataac_account_id}:role/mart-role-${var.environment}"
                }
                Action = [
                    "s3:ReplicateObject",
                    "s3:ReplicateDelete",
                    "s3:ReplicateTags"
                ]
                Resource = "arn:aws:s3:::dpda-async-mart-api-output-${var.environment_suffix}/*"
            },
            {
                Sid    = "Permissions on bucket"
                Effect = "Allow"
                Principal = {
                    AWS = "arn:aws:iam::${var.dataac_account_id}:role/mart-role-${var.environment}"
                }
                Action = [
                    "s3:GetBucketVersioning",
                    "s3:PutBucketVersioning"
                ]
                Resource = "arn:aws:s3:::dpda-async-mart-api-output-${var.environment_suffix}"
            }
        ]
    })
}