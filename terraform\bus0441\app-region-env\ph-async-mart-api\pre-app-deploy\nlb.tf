resource "aws_lb_target_group" "ph_async_mart_api_nlb_fargate_target_group" {
  name        = "ph-async-mart-api-tg-nlb-${var.environment}"
  target_type = "ip"
  port        = 8080
  protocol    = "TCP"
  vpc_id      = var.vpc

  health_check {
    healthy_threshold   = "3"
    interval            = "30"
    protocol            = "HTTP"
    matcher             = "200-399"
    timeout             = "3"
    path                = "/portfolio-holdings-api/v1/health-check"
    unhealthy_threshold = "3"
  }
  tags = local.tags
}