#
#Configuration for lambda function: release-management-tool
#
# create lambda resource
resource "aws_lambda_function" "release-management-tool-lambda" {
  count                          = var.release_management_lambda_count
  description                    = "aws:states:opt-out"
  s3_bucket                      = local.lambda_s3_bucket
  s3_key                         = "entitlement-lambda/release-management-tool/${var.release_version}/app.jar"
  function_name                  = "release-management-tool-${var.environment}"
  role                           = data.aws_iam_role.entitlement-role.arn
  layers                         = [var.extension_arn]
  handler                        = "ReleaseManagementJob::handleRequest"
  runtime                        = "java17"
  timeout                        = "180"
  memory_size                    = "512"
  reserved_concurrent_executions = "5"

  vpc_config {
    subnet_ids = flatten([
      module.region_data.private_subnet_1_id,
      module.region_data.private_subnet_2_id,
      module.region_data.private_subnet_3_id,
    ])

    security_group_ids = flatten([
      module.region_data.security_group_private_web,
      module.region_data.security_group_private_db,
      module.region_data.security_group_private_app
    ])
  }

  tags = local.tags

  environment {
    variables = {
      velo_profiles_active        = "${var.environment}-${data.aws_region.current.name}"
      aws_region                  = data.aws_region.current.name
    }
  }
}


#
#Configuration for lambda function: entitlement-sync-uim-email-to-user
#
# create lambda resource
resource "aws_lambda_function" "entitlement-sync-uim-email-to-user-lambda" {
  description                    = "aws:states:opt-out"
  s3_bucket                      = local.lambda_s3_bucket
  s3_key                         = "entitlement-lambda/entitlement-sync-uim-email-to-user/${var.release_version}/app.jar"
  function_name                  = "velo-entitlement-sync-email-info-from-UIM-${var.environment}"
  role                           = data.aws_iam_role.entitlement-role.arn
  layers                          = [var.extension_arn]
  handler                        = "UIMUserTopicSNSHandler::handleRequest"
  runtime                        = "java17"
  timeout                        = "180"
  memory_size                    = "1024"
  reserved_concurrent_executions = "5"

  vpc_config {
    subnet_ids = flatten([
      module.region_data.private_subnet_1_id,
      module.region_data.private_subnet_2_id,
      module.region_data.private_subnet_3_id,
    ])

    security_group_ids = flatten([
      module.region_data.security_group_private_web,
      module.region_data.security_group_private_db,
      module.region_data.security_group_private_app
    ])
  }

  tags = local.tags

  environment {
    variables = {
      velo_profiles_active        = "${var.environment}-${data.aws_region.current.name}"
      aws_region                  = data.aws_region.current.name
      datasource_session_variable = var.datasource_session_variable
      parameters_secrets_extension_http_port = var.port
    }
  }
}
#lambda permission
resource "aws_lambda_permission" "entitlement-sync-uim-email-to-user-sns" {
  statement_id  = "${var.environment}-entitlement-sync-uim-email-to-user-sns"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.entitlement-sync-uim-email-to-user-lambda.function_name
  principal     = "sns.amazonaws.com"
  source_arn    = var.uim_email_arn
}

#
#Configuration for lambda function: entitlement-sync-ams-users
#
# create lambda resource
resource "aws_lambda_function" "entitlement-sync-ams-users-lambda" {
  description                    = "aws:states:opt-out"
  s3_bucket                      = local.lambda_s3_bucket
  s3_key                         = "entitlement-lambda/entitlement-sync-ams-users/${var.release_version}/app.jar"
  function_name                  = "entitlement-sync-ams-users-${var.environment}"
  role                           = data.aws_iam_role.entitlement-role.arn
  layers                         = [var.extension_arn]
  handler                        = "AmsSyncUserJob::handleRequest"
  runtime                        = "java17"
  timeout                        = "180"
  memory_size                    = "1024"
  reserved_concurrent_executions = "5"

  vpc_config {
    subnet_ids = flatten([
      module.region_data.private_subnet_1_id,
      module.region_data.private_subnet_2_id,
      module.region_data.private_subnet_3_id,
    ])

    security_group_ids = flatten([
      module.region_data.security_group_private_web,
      module.region_data.security_group_private_db,
      module.region_data.security_group_private_app
    ])
  }

  tags = local.tags

  environment {
    variables = {
      velo_profiles_active        = "${var.environment}-${data.aws_region.current.name}"
      aws_region                  = data.aws_region.current.name
      datasource_session_variable = var.datasource_session_variable
      parameters_secrets_extension_http_port = var.port
    }
  }
}
# Allow Cloud Watch to Call Lambda
resource "aws_lambda_permission" "allow_cloudwatch_to_call_entitlement_sync_ams_users" {
  statement_id = "AllowExecutionFromCloudWatch"
  action = "lambda:InvokeFunction"
  function_name = aws_lambda_function.entitlement-sync-ams-users-lambda.function_name
  principal = "events.amazonaws.com"
  source_arn = aws_cloudwatch_event_rule.every_day_every_four_hour_ams_users.arn
}

#
#Configuration for lambda function: entitlement-sync-data-group-permission
#
# create lambda resource
resource "aws_lambda_function" "entitlement-sync-data-group-permission-lambda" {
  description                    = "aws:states:opt-out"
  s3_bucket                      = local.lambda_s3_bucket
  s3_key                         = "entitlement-lambda/entitlement-sync-data-group-permission/${var.release_version}/app.jar"
  function_name                  = "entitlement-sync-data-group-permission-${var.environment}"
  role                           = "${data.aws_iam_role.entitlement-role.arn}"
  layers                         = [var.extension_arn]
  handler                        = "SyncDataGroupPermissionHandler::handleRequest"
  runtime                        = "java17"
  timeout                        = "180"
  memory_size                    = "1024"
  reserved_concurrent_executions = "5"

  vpc_config {
    subnet_ids = flatten([
      module.region_data.private_subnet_1_id,
      module.region_data.private_subnet_2_id,
      module.region_data.private_subnet_3_id,
    ])

    security_group_ids = flatten([
      module.region_data.security_group_private_web,
      module.region_data.security_group_private_db,
      module.region_data.security_group_private_app
    ])
  }

  tags = local.tags

  environment {
    variables = {
      velo_profiles_active        = "${var.environment}-${data.aws_region.current.name}"
      aws_region                  = data.aws_region.current.name
      datasource_session_variable = var.datasource_session_variable
      parameters_secrets_extension_http_port = var.port
    }
  }
}
# Allow Cloud Watch to Call Lambda
resource "aws_lambda_permission" "allow_cloudwatch_to_call_entitlement_sync_data_group_permission" {
  statement_id = "AllowExecutionFromCloudWatch"
  action = "lambda:InvokeFunction"
  function_name = aws_lambda_function.entitlement-sync-data-group-permission-lambda.function_name
  principal = "events.amazonaws.com"
  source_arn = aws_cloudwatch_event_rule.every_day_at_two_am_cst.arn
}

#
#Configuration for lambda function: entitlement-sync-private-index-data
#
# create lambda resource for entitlement-sync-private-index-data
resource "aws_lambda_function" "entitlement-sync-private-index-data-lambda" {
  description                    = "aws:states:opt-out"
  s3_bucket                      = local.lambda_s3_bucket
  s3_key                         = "entitlement-lambda/entitlement-sync-private-index-data/${var.release_version}/app.jar"
  function_name                  = "entitlement-sync-private-index-data-${var.environment}"
  role                           = "${data.aws_iam_role.entitlement-role.arn}"
  layers                         = [var.extension_arn]
  handler                        = "SyncPrivateIndexPermissionHandler::handleRequest"
  runtime                        = "java17"
  timeout                        = "180"
  memory_size                    = "1024"
  reserved_concurrent_executions = "5"

  vpc_config {
    subnet_ids = flatten([
      module.region_data.private_subnet_1_id,
      module.region_data.private_subnet_2_id,
      module.region_data.private_subnet_3_id,
    ])

    security_group_ids = flatten([
      module.region_data.security_group_private_web,
      module.region_data.security_group_private_db,
      module.region_data.security_group_private_app
    ])
  }

  tags = local.tags

  environment {
    variables = {
      velo_profiles_active        = "${var.environment}-${data.aws_region.current.name}"
      aws_region                  = data.aws_region.current.name
      datasource_session_variable = var.datasource_session_variable
      parameters_secrets_extension_http_port = var.port
    }
  }
}
# Allow Cloud Watch to Call Lambda
resource "aws_lambda_permission" "allow_cloudwatch_to_call_entitlement-sync-private-index-data" {
  statement_id = "AllowExecutionFromCloudWatch"
  action = "lambda:InvokeFunction"
  function_name = aws_lambda_function.entitlement-sync-private-index-data-lambda.function_name
  principal = "events.amazonaws.com"
  source_arn = aws_cloudwatch_event_rule.every_day_at_three_am_cst.arn
}

#
#Configuration for lambda function: entitlement-sync-salesforce-data
#
# create lambda resource for entitlement-sync-salesforce-data
resource "aws_lambda_function" "entitlement-sync-salesforce-data-lambda" {
  description                    = "aws:states:opt-out"
  s3_bucket                      = local.lambda_s3_bucket
  s3_key                         = "entitlement-lambda/entitlement-sync-salesforce-data/${var.release_version}/app.jar"
  function_name                  = "entitlement-sync-salesforce-data-${var.environment}"
  role                           = "${data.aws_iam_role.entitlement-role.arn}"
  layers                         = [var.extension_arn]
  handler                        = "SyncSalesforceDataHandler::handleRequest"
  runtime                        = "java17"
  timeout                        = "180"
  memory_size                    = "1024"
  reserved_concurrent_executions = "5"

  vpc_config {
    subnet_ids = flatten([
      module.region_data.private_subnet_1_id,
      module.region_data.private_subnet_2_id,
      module.region_data.private_subnet_3_id,
    ])

    security_group_ids = flatten([
      module.region_data.security_group_private_web,
      module.region_data.security_group_private_db,
      module.region_data.security_group_private_app
    ])
  }

  tags = local.tags

  environment {
    variables = {
      velo_profiles_active        = "${var.environment}-${data.aws_region.current.name}"
      aws_region                  = data.aws_region.current.name
      datasource_session_variable = var.datasource_session_variable
      parameters_secrets_extension_http_port = var.port
    }
  }
}
# Allow Cloud Watch to Call Lambda
resource "aws_lambda_permission" "allow_cloudwatch_to_call_entitlement-sync-salesforce-data" {
  statement_id = "AllowExecutionFromCloudWatch"
  action = "lambda:InvokeFunction"
  function_name = aws_lambda_function.entitlement-sync-salesforce-data-lambda.function_name
  principal = "events.amazonaws.com"
  source_arn = aws_cloudwatch_event_rule.every_day_every_two_hours.arn
}

#
#Configuration for lambda function: entitlement-sync-inactive-user-permission
#
# create lambda resource
resource "aws_lambda_function" "entitlement-sync-inactive-user-permission-lambda" {
  description                    = "aws:states:opt-out"
  s3_bucket                      = local.lambda_s3_bucket
  s3_key                         = "entitlement-lambda/entitlement-sync-inactive-user-permission/${var.release_version}/app.jar"
  function_name                  = "entitlement-sync-inactive-user-permission-${var.environment}"
  role                           = data.aws_iam_role.entitlement-role.arn
  layers                         = [var.extension_arn]
  handler                        = "SyncInactiveUserPermissionHandler::handleRequest"
  runtime                        = "java17"
  timeout                        = "180"
  memory_size                    = "1024"
  reserved_concurrent_executions = "5"

  vpc_config {
    subnet_ids = flatten([
      module.region_data.private_subnet_1_id,
      module.region_data.private_subnet_2_id,
      module.region_data.private_subnet_3_id,
    ])

    security_group_ids = flatten([
      module.region_data.security_group_private_web,
      module.region_data.security_group_private_db,
      module.region_data.security_group_private_app
    ])
  }

  tags = local.tags

  environment {
    variables = {
      velo_profiles_active        = "${var.environment}-${data.aws_region.current.name}"
      aws_region                  = data.aws_region.current.name
      datasource_session_variable = var.datasource_session_variable
      parameters_secrets_extension_http_port = var.port
    }
  }
}
# Allow Cloud Watch to Call Lambda
resource "aws_lambda_permission" "allow_cloudwatch_to_call_entitlement-sync-inactive-user-permission" {
  statement_id = "AllowExecutionFromCloudWatch"
  action = "lambda:InvokeFunction"
  function_name = aws_lambda_function.entitlement-sync-inactive-user-permission-lambda.function_name
  principal = "events.amazonaws.com"
  source_arn = aws_cloudwatch_event_rule.every_day_at_four_am_cst.arn
}