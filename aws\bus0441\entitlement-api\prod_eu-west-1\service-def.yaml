launchType: FARGATE
serviceName: prod-entitlement-api-service-nlb
desiredCount: 0
networkConfiguration:
  awsvpcConfiguration:
    securityGroups:
      - sg-0e0437a4ad0633a99 # private_app
      - sg-0e59dd6a8457e658d # private_web
      - sg-0ab3f01d10c8c197a # private_db
    subnets:
      - subnet-033bddf5b679971ba # eu-west-1c-private
      - subnet-028c31b8c943d1a56 # eu-west-1b-private
      - subnet-09ccfc3620f652857 # eu-west-1a-private
    assignPublicIp: DISABLED

deploymentConfiguration:
  maximumPercent: 200
  minimumHealthyPercent: 50

healthCheckGracePeriodSeconds: 300

loadBalancers:
  - targetGroupArn: <+execution.steps.Terraform_Pre_App_Deploy.steps.TerraformApply.output.target_group_arn>
    containerName: prod-entitlement-api
    containerPort: 8443

tags:
  - key: SERVICEID
    value: <+serviceVariables.SERVICEID>
  - key: ORG
    value: <+serviceVariables.ORG>
  - key: TID
    value: <+variable.TID>
  - key: PID
    value: <+serviceVariables.PID>
  - key: MANAGED
    value: <+serviceVariables.MANAGED>
  - key: ENVIRONMENT
    value: <+env.variables.AWS_ENV_TAG>

propagateTags: TASK_DEFINITION