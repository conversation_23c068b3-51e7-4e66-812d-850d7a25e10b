variable "region" {
  default     = "us-east-1"
  description = "The region of the resources"
}

variable "environment" {
  description = "The environment"
}

variable "environmentForTagging" {
  description = "The environment for AWS Resource Tagging"
}

variable "session_name" {
  type        = string
  default     = "TerraformSessionName"
  description = "AWS role assumption session name"
}

variable "role_to_assume" {
  type    = string
  default = null
}

variable "use_role_to_assume" {
  type    = string
  default = true
}

variable "release_version" {
  type = string
  default = ""
}


##################################################################
####### ALL Custom Variables should go below this section ########
##################################################################

variable "config_trigger_url" {
  description = "api to trigger config update"
}

variable "required" {
  default = 0
}

variable "rdb_topic_sqs" {
  description = "rdb sqs tropic"
  default = "arn:aws:sns:us-east-1:************:dbops-rdb-flattablestatus"
}

variable "account_env" {
  description = "s3 environment"
  default = "nonprod"
}

variable "port" {
  default = "2773"
}

variable "extension_arn" {
  description = "Extension ARNs for the x86_64"
}

variable "bucket_for_config_update"{
  description = "S3 bucket source to trigger config-update lambda"
}

variable "dataac_account_id" {
  description = "DATAAC AWS account ID where the mart-data buckets reside"
  type        = string
}

locals {
  tags = {
    MANAGED     = "terraform"
    ENVIRONMENT = var.environmentForTagging,
    PID         = "PID0462",
    SERVICEID   = "ts01695",
    TID         = "DATAAC",
  }
}

variable "rdb_sqs_alarm_monitor_count" {
  default = 1
}

variable "rdb_sqs_alarm_threashold" {
  default = 1800
}

variable "rdb_sqs_alarm_period" {
  default = 300
}

variable rdb_sqs_alarm_evaluation_periods {
    default = 1
}


