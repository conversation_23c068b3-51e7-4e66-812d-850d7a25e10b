resource "aws_cloudwatch_log_group" "async_mart_api_fargate_log" {
  name              = "/ecs/async-mart-api-fargate-${var.environment}"
  retention_in_days = "7"
  tags              = local.tags
}

resource "aws_cloudwatch_log_subscription_filter" "async_mart_api_fargate_log_subscription" {
  name            = "async-mart-api-fargate-log-subscription-${var.environment}"
  log_group_name  = aws_cloudwatch_log_group.async_mart_api_fargate_log.name
  filter_pattern  = ""
  destination_arn = data.aws_kinesis_stream.stream.arn
  role_arn        = data.aws_iam_role.log_role.arn
}

resource "aws_cloudwatch_metric_alarm" "asyncapi_sqs_overflow_alarm" {
  alarm_name          = "asyncapi-sqs-overflow-alarm-${var.environment}"
  count               = var.required
  comparison_operator = "GreaterThanOrEqualToThreshold"
  metric_name         = "ApproximateNumberOfMessagesVisible"
  namespace           = "AWS/SQS"
  period              = 300
  evaluation_periods  = 1
  statistic           = "Average"
  threshold           = 50
  alarm_description   = "This metric monitors sqs queue"
  treat_missing_data  = "ignore"
  dimensions = {
    QueueName = aws_sqs_queue.async-mart-api-message.name
  }
  alarm_actions             = [aws_sns_topic.asyncapi_notif[count.index].arn]
  ok_actions                = [aws_sns_topic.asyncapi_notif[count.index].arn]
  insufficient_data_actions = []
  depends_on                = [aws_sns_topic.asyncapi_notif]
  tags                      = local.tags
}

resource "aws_cloudwatch_metric_alarm" "asyncapi_sqs_stalled_message_alarm" {
  alarm_name          = "asyncapi-sqs-stalled-message-alarm-${var.environment}"
  count               = var.required
  comparison_operator = "GreaterThanOrEqualToThreshold"
  metric_name         = "ApproximateAgeOfOldestMessage"
  namespace           = "AWS/SQS"
  period              = 300
  evaluation_periods  = 1
  statistic           = "Average"
  threshold           = 21600
  alarm_description   = "This metric monitors sqs queue for stalled messages"
  treat_missing_data  = "ignore"
  dimensions = {
    QueueName = aws_sqs_queue.async-mart-api-message.name
  }
  alarm_actions             = [aws_sns_topic.asyncapi_notif[count.index].arn]
  ok_actions                = [aws_sns_topic.asyncapi_notif[count.index].arn]
  insufficient_data_actions = []
  depends_on                = [aws_sns_topic.asyncapi_notif]
  tags                      = local.tags
}
